{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "ad1a805a21cf56a7070dde24f5220686", "files": {"client_py": {"hash": "f4070b19f21d8f32ebf670a257333687", "index": {"url": "client_py.html", "file": "client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 214, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "config_loader_py": {"hash": "8a7c24a88f1ad9771355e35727778231", "index": {"url": "config_loader_py.html", "file": "config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "create_wrappers_py": {"hash": "a8350ea624d5c59ecd74b1c626120512", "index": {"url": "create_wrappers_py.html", "file": "create_wrappers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "generate_certs_py": {"hash": "a393e1294a1b7df0e03baf05e98f9db0", "index": {"url": "generate_certs_py.html", "file": "generate_certs.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 149, "n_excluded": 0, "n_missing": 149, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "generate_performance_report_py": {"hash": "0467864e4b3930e6dc92a122be07ba2c", "index": {"url": "generate_performance_report_py.html", "file": "generate_performance_report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 337, "n_excluded": 0, "n_missing": 337, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "generate_speed_report_py": {"hash": "b524969249ecf97a392b86002455a6a7", "index": {"url": "generate_speed_report_py.html", "file": "generate_speed_report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "performance_test_client_py": {"hash": "08b5632acd3f43691a4381d31692e829", "index": {"url": "performance_test_client_py.html", "file": "performance_test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "run_comprehensive_tests_py": {"hash": "af789b8680c5c659ca9bac1f5807ae14", "index": {"url": "run_comprehensive_tests_py.html", "file": "run_comprehensive_tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "search_algorithms_new_py": {"hash": "25771c031d55919b2c390021c1f6c8b0", "index": {"url": "search_algorithms_new_py.html", "file": "search_algorithms_new.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "server_py": {"hash": "23fea5d9d76dbdad535457f06f3372ad", "index": {"url": "server_py.html", "file": "server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 188, "n_excluded": 0, "n_missing": 61, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_client_config_py": {"hash": "7f86b797768a5bace39ebd24e5d0dd0d", "index": {"url": "test_client_config_py.html", "file": "test_client_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "test_installations_py": {"hash": "2ef730649ca1bc6d1c4e145133feb610", "index": {"url": "test_installations_py.html", "file": "test_installations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 110, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531___init___py": {"hash": "de07d371a255edf517c240779d083893", "index": {"url": "z_a44f0ac069e85531___init___py.html", "file": "tests/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_client_py": {"hash": "0354b56a5f5ca228d31a988bd3b4f02f", "index": {"url": "z_a44f0ac069e85531_test_client_py.html", "file": "tests/test_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 263, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_config_py": {"hash": "572754ac47cc33de0bbd53a370fce646", "index": {"url": "z_a44f0ac069e85531_test_config_py.html", "file": "tests/test_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_exception_handling_py": {"hash": "0c49ccbdd6d1af8a9cfcd44f34aef7d0", "index": {"url": "z_a44f0ac069e85531_test_exception_handling_py.html", "file": "tests/test_exception_handling.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_generate_speed_report_py": {"hash": "15819a195aabc0ec83d48570a78e0eac", "index": {"url": "z_a44f0ac069e85531_test_generate_speed_report_py.html", "file": "tests/test_generate_speed_report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 160, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_hashset_py": {"hash": "26424a1d17afd020de18b30e81cfa884", "index": {"url": "z_a44f0ac069e85531_test_hashset_py.html", "file": "tests/test_hashset.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_load_testing_py": {"hash": "1291668f138097c975be5853cc784655", "index": {"url": "z_a44f0ac069e85531_test_load_testing_py.html", "file": "tests/test_load_testing.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_performance_comprehensive_py": {"hash": "0d68107eb32c8114689a3096a63f0140", "index": {"url": "z_a44f0ac069e85531_test_performance_comprehensive_py.html", "file": "tests/test_performance_comprehensive.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_search_py": {"hash": "96d4169a605b85e715dc56751e39004a", "index": {"url": "z_a44f0ac069e85531_test_search_py.html", "file": "tests/test_search.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_search_algorithms_py": {"hash": "136c268246d803605c166be72c82665a", "index": {"url": "z_a44f0ac069e85531_test_search_algorithms_py.html", "file": "tests/test_search_algorithms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 290, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a44f0ac069e85531_test_server_py": {"hash": "0f38b0874e404820130c6907d2226c7a", "index": {"url": "z_a44f0ac069e85531_test_server_py.html", "file": "tests/test_server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 688, "n_excluded": 0, "n_missing": 299, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "verify_installation_py": {"hash": "9a02ddfde59f47a1ea07d7db7eeaa024", "index": {"url": "verify_installation_py.html", "file": "verify_installation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 0, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}