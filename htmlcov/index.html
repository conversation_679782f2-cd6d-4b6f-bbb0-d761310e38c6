<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">60%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 22:11 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="client_py.html">client.py</a></td>
                <td>214</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="138 214">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td>75</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="64 75">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_wrappers_py.html">create_wrappers.py</a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html">generate_certs.py</a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html">generate_performance_report.py</a></td>
                <td>337</td>
                <td>337</td>
                <td>0</td>
                <td class="right" data-ratio="0 337">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html">generate_speed_report.py</a></td>
                <td>127</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="122 127">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html">performance_test_client.py</a></td>
                <td>187</td>
                <td>187</td>
                <td>0</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_comprehensive_tests_py.html">run_comprehensive_tests.py</a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html">search_algorithms_new.py</a></td>
                <td>232</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="197 232">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html">server.py</a></td>
                <td>188</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="127 188">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_client_config_py.html">test_client_config.py</a></td>
                <td>45</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="34 45">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html">test_installations.py</a></td>
                <td>169</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="59 169">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html">tests/test_client.py</a></td>
                <td>263</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="240 263">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html">tests/test_config.py</a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html">tests/test_exception_handling.py</a></td>
                <td>196</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="192 196">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html">tests/test_generate_speed_report.py</a></td>
                <td>160</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="158 160">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_hashset_py.html">tests/test_hashset.py</a></td>
                <td>29</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="17 29">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html">tests/test_load_testing.py</a></td>
                <td>260</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="235 260">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html">tests/test_performance_comprehensive.py</a></td>
                <td>211</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="197 211">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html">tests/test_search.py</a></td>
                <td>57</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html">tests/test_search_algorithms.py</a></td>
                <td>290</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="286 290">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html">tests/test_server.py</a></td>
                <td>688</td>
                <td>299</td>
                <td>0</td>
                <td class="right" data-ratio="389 688">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html">verify_installation.py</a></td>
                <td>165</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="0 165">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>4192</td>
                <td>1660</td>
                <td>0</td>
                <td class="right" data-ratio="2532 4192">60%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 22:11 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="verify_installation_py.html"></a>
        <a id="nextFileLink" class="nav" href="client_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
