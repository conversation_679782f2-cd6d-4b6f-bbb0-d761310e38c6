<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">60%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 22:11 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t32">client.py</a></td>
                <td class="name left"><a href="client_py.html#t32"><data value='init__'>SearchClient.__init__</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t68">client.py</a></td>
                <td class="name left"><a href="client_py.html#t68"><data value='find_ca_file'>SearchClient._find_ca_file</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t81">client.py</a></td>
                <td class="name left"><a href="client_py.html#t81"><data value='create_ssl_context'>SearchClient.create_ssl_context</data></a></td>
                <td>20</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="18 20">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t116">client.py</a></td>
                <td class="name left"><a href="client_py.html#t116"><data value='connect'>SearchClient.connect</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t140">client.py</a></td>
                <td class="name left"><a href="client_py.html#t140"><data value='disconnect'>SearchClient.disconnect</data></a></td>
                <td>6</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="1 6">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t149">client.py</a></td>
                <td class="name left"><a href="client_py.html#t149"><data value='send_query_persistent'>SearchClient.send_query_persistent</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t183">client.py</a></td>
                <td class="name left"><a href="client_py.html#t183"><data value='search'>SearchClient.search</data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t236">client.py</a></td>
                <td class="name left"><a href="client_py.html#t236"><data value='interactive_mode'>SearchClient.interactive_mode</data></a></td>
                <td>32</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="3 32">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t281">client.py</a></td>
                <td class="name left"><a href="client_py.html#t281"><data value='test_connection'>SearchClient.test_connection</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t295">client.py</a></td>
                <td class="name left"><a href="client_py.html#t295"><data value='load_config'>load_config</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t326">client.py</a></td>
                <td class="name left"><a href="client_py.html#t326"><data value='main'>main</data></a></td>
                <td>45</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="40 45">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html">client.py</a></td>
                <td class="name left"><a href="client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t54">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t54"><data value='find_config_file'>find_config_file</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t79">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t79"><data value='load_config'>load_config</data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t129">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t129"><data value='resolve_cert_path'>load_config.resolve_cert_path</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t166">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t166"><data value='get_file_path'>get_file_path</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_wrappers_py.html#t9">create_wrappers.py</a></td>
                <td class="name left"><a href="create_wrappers_py.html#t9"><data value='main'>main</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_wrappers_py.html">create_wrappers.py</a></td>
                <td class="name left"><a href="create_wrappers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t19">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t19"><data value='run_command'>run_command</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t44">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t44"><data value='create_cert_directory'>create_cert_directory</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t57">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t57"><data value='generate_ca_certificate'>generate_ca_certificate</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t88">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t88"><data value='generate_server_certificate'>generate_server_certificate</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t164">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t164"><data value='generate_client_certificate'>generate_client_certificate</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t212">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t212"><data value='set_permissions'>set_permissions</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t235">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t235"><data value='verify_certificates'>verify_certificates</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html#t260">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html#t260"><data value='main'>main</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t52">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t52"><data value='init__'>PerformanceReportGenerator.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t63">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t63"><data value='run_server_performance_tests'>PerformanceReportGenerator.run_server_performance_tests</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t127">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t127"><data value='generate_performance_charts'>PerformanceReportGenerator.generate_performance_charts</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t151">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t151"><data value='create_benchmark_chart'>PerformanceReportGenerator._create_benchmark_chart</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t186">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t186"><data value='create_load_test_chart'>PerformanceReportGenerator._create_load_test_chart</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t221">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t221"><data value='create_sustained_load_chart'>PerformanceReportGenerator._create_sustained_load_chart</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t253">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t253"><data value='generate_markdown_report'>PerformanceReportGenerator.generate_markdown_report</data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t343">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t343"><data value='save_raw_data'>PerformanceReportGenerator.save_raw_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t352">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t352"><data value='generate_pdf_report'>PerformanceReportGenerator.generate_pdf_report</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t525">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t525"><data value='generate_full_report'>PerformanceReportGenerator.generate_full_report</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t573">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t573"><data value='main'>main</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t40">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t40"><data value='load'>SearchAlgorithm.load</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t41">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t41"><data value='search'>SearchAlgorithm.search</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t44">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t44"><data value='name'>SearchAlgorithm.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t47">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t47"><data value='benchmark_algorithm'>benchmark_algorithm</data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t107">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t107"><data value='create_test_queries'>create_test_queries</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t144">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t144"><data value='plot_comparison'>plot_comparison</data></a></td>
                <td>30</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="29 30">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t214">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t214"><data value='generate_report'>generate_report</data></a></td>
                <td>48</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="47 48">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="18 21">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t34">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t34"><data value='init__'>PerformanceTestClient.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t51">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t51"><data value='create_connection'>PerformanceTestClient.create_connection</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t65">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t65"><data value='single_query'>PerformanceTestClient.single_query</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t98">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t98"><data value='benchmark_queries'>PerformanceTestClient.benchmark_queries</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t187">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t187"><data value='concurrent_load_test'>PerformanceTestClient.concurrent_load_test</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t208">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t208"><data value='worker_thread'>PerformanceTestClient.concurrent_load_test.worker_thread</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t321">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t321"><data value='test_connection'>PerformanceTestClient.test_connection</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t330">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t330"><data value='create_test_queries'>create_test_queries</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t358">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t358"><data value='main'>main</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_comprehensive_tests_py.html#t19">run_comprehensive_tests.py</a></td>
                <td class="name left"><a href="run_comprehensive_tests_py.html#t19"><data value='run_command'>run_command</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_comprehensive_tests_py.html#t52">run_comprehensive_tests.py</a></td>
                <td class="name left"><a href="run_comprehensive_tests_py.html#t52"><data value='main'>main</data></a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_comprehensive_tests_py.html">run_comprehensive_tests.py</a></td>
                <td class="name left"><a href="run_comprehensive_tests_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t41">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t41"><data value='load'>SearchAlgorithm.load</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t55">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t55"><data value='search'>SearchAlgorithm.search</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t70">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t70"><data value='name'>SearchAlgorithm.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t103">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t103"><data value='init__'>HashSetSearch.__init__</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t130">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t130"><data value='load'>HashSetSearch.load</data></a></td>
                <td>19</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="17 19">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t186">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t186"><data value='fast_load'>HashSetSearch.fast_load</data></a></td>
                <td>38</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="22 38">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t271">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t271"><data value='simple_search'>HashSetSearch._simple_search</data></a></td>
                <td>22</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="16 22">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t322">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t322"><data value='search'>HashSetSearch.search</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t350">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t350"><data value='name'>HashSetSearch.name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t371">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t371"><data value='init__'>LinearSearch.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t381">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t381"><data value='load'>LinearSearch.load</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t390">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t390"><data value='search'>LinearSearch.search</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t412">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t412"><data value='name'>LinearSearch.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t431">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t431"><data value='init__'>BinarySearch.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t442">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t442"><data value='load'>BinarySearch.load</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t462">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t462"><data value='search'>BinarySearch.search</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t481">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t481"><data value='name'>BinarySearch.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t501">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t501"><data value='init__'>MMapSearch.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t513">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t513"><data value='load'>MMapSearch.load</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t531">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t531"><data value='build_line_index'>MMapSearch._build_line_index</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t549">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t549"><data value='search'>MMapSearch.search</data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t586">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t586"><data value='name'>MMapSearch.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t589">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t589"><data value='del__'>MMapSearch.__del__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t612">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t612"><data value='init__'>GrepSearch.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t622">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t622"><data value='load'>GrepSearch.load</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t631">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t631"><data value='search'>GrepSearch.search</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t657">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t657"><data value='name'>GrepSearch.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="46 51">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t44">server.py</a></td>
                <td class="name left"><a href="server_py.html#t44"><data value='init__'>FileSearchEngine.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t67">server.py</a></td>
                <td class="name left"><a href="server_py.html#t67"><data value='validate_file'>FileSearchEngine._validate_file</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t75">server.py</a></td>
                <td class="name left"><a href="server_py.html#t75"><data value='search'>FileSearchEngine.search</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t98">server.py</a></td>
                <td class="name left"><a href="server_py.html#t98"><data value='setup_logging'>setup_logging</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t133">server.py</a></td>
                <td class="name left"><a href="server_py.html#t133"><data value='handle'>StringSearchHandler.handle</data></a></td>
                <td>31</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="22 31">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t218">server.py</a></td>
                <td class="name left"><a href="server_py.html#t218"><data value='init__'>StringSearchServer.__init__</data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t270">server.py</a></td>
                <td class="name left"><a href="server_py.html#t270"><data value='server_bind'>StringSearchServer.server_bind</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t279">server.py</a></td>
                <td class="name left"><a href="server_py.html#t279"><data value='handle_error'>StringSearchServer.handle_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t286">server.py</a></td>
                <td class="name left"><a href="server_py.html#t286"><data value='shutdown_request'>StringSearchServer.shutdown_request</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t295">server.py</a></td>
                <td class="name left"><a href="server_py.html#t295"><data value='create_ssl_context'>create_ssl_context</data></a></td>
                <td>38</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="28 38">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t380">server.py</a></td>
                <td class="name left"><a href="server_py.html#t380"><data value='main'>main</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html">server.py</a></td>
                <td class="name left"><a href="server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="28 29">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_client_config_py.html">test_client_config.py</a></td>
                <td class="name left"><a href="test_client_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="34 45">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t22">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t22"><data value='print_header'>print_header</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t29">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t29"><data value='print_status'>print_status</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t35">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t35"><data value='test_tcp_connection'>test_tcp_connection</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t52">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t52"><data value='test_system_installation'>test_system_installation</data></a></td>
                <td>17</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="6 17">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t82">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t82"><data value='test_user_installation'>test_user_installation</data></a></td>
                <td>21</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="6 21">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t126">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t126"><data value='create_wrapper_scripts'>create_wrapper_scripts</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t184">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t184"><data value='test_user_commands'>test_user_commands</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t212">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t212"><data value='test_client_functionality'>test_client_functionality</data></a></td>
                <td>13</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="7 13">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t237">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t237"><data value='cleanup_user_installation'>cleanup_user_installation</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html#t274">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html#t274"><data value='main'>main</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="19 20">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests/__init__.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t19">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t19"><data value='test_client_initialization'>TestSearchClient.test_client_initialization</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t31">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t31"><data value='test_client_initialization_with_custom_params'>TestSearchClient.test_client_initialization_with_custom_params</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t52">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t52"><data value='test_create_ssl_context_default'>TestSearchClient.test_create_ssl_context_default</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t59">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t59"><data value='test_create_ssl_context_with_ca_file'>TestSearchClient.test_create_ssl_context_with_ca_file</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t68">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t68"><data value='test_create_ssl_context_no_verify'>TestSearchClient.test_create_ssl_context_no_verify</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t75">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t75"><data value='test_create_ssl_context_with_client_cert'>TestSearchClient.test_create_ssl_context_with_client_cert</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t87">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t87"><data value='test_search_successful_non_ssl'>TestSearchClient.test_search_successful_non_ssl</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t104">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t104"><data value='test_search_successful_with_ssl'>TestSearchClient.test_search_successful_with_ssl</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t128">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t128"><data value='test_search_query_too_long'>TestSearchClient.test_search_query_too_long</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t137">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t137"><data value='test_search_connection_timeout'>TestSearchClient.test_search_connection_timeout</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t149">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t149"><data value='test_search_connection_error'>TestSearchClient.test_search_connection_error</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t161">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t161"><data value='test_search_socket_cleanup_on_exception'>TestSearchClient.test_search_socket_cleanup_on_exception</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t174">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t174"><data value='test_test_connection_success'>TestSearchClient.test_test_connection_success</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t185">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t185"><data value='test_test_connection_failure'>TestSearchClient.test_test_connection_failure</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t196">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t196"><data value='test_search_unicode_query'>TestSearchClient.test_search_unicode_query</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t210">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t210"><data value='test_search_empty_response'>TestSearchClient.test_search_empty_response</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t223">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t223"><data value='test_search_response_with_whitespace'>TestSearchClient.test_search_response_with_whitespace</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t235">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t235"><data value='test_search_timing_accuracy'>TestSearchClient.test_search_timing_accuracy</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t242">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t242"><data value='delayed_connect'>TestSearchClient.test_search_timing_accuracy.delayed_connect</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t260">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t260"><data value='test_main_test_connection_success'>TestMainFunction.test_main_test_connection_success</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t272">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t272"><data value='test_main_test_connection_failure'>TestMainFunction.test_main_test_connection_failure</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t284">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t284"><data value='test_main_single_query_success'>TestMainFunction.test_main_single_query_success</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t297">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t297"><data value='test_main_single_query_error'>TestMainFunction.test_main_single_query_error</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t309">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t309"><data value='test_main_interactive_mode'>TestMainFunction.test_main_interactive_mode</data></a></td>
                <td>8</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="4 8">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t325">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t325"><data value='test_main_interactive_keyboard_interrupt'>TestMainFunction.test_main_interactive_keyboard_interrupt</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t338">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t338"><data value='test_main_interactive_exception_handling'>TestMainFunction.test_main_interactive_exception_handling</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t350">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t350"><data value='test_main_if_name_main_coverage'>TestMainFunction.test_main_if_name_main_coverage</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t366">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t366"><data value='test_main_custom_connection_params'>TestMainFunction.test_main_custom_connection_params</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t391">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t391"><data value='test_main_help_display'>TestMainFunction.test_main_help_display</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>75</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="74 75">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html#t23">tests/test_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html#t23"><data value='test_config_loading'>test_config_loading</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html#t33">tests/test_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html#t33"><data value='test_invalid_config_file'>test_invalid_config_file</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html#t38">tests/test_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html#t38"><data value='test_required_parameters'>test_required_parameters</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html">tests/test_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t42">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t42"><data value='temp_dir'>TestFileOperationExceptions.temp_dir</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t47">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t47"><data value='test_file_not_found_error'>TestFileOperationExceptions.test_file_not_found_error</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t55">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t55"><data value='test_file_permission_denied'>TestFileOperationExceptions.test_file_permission_denied</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t72">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t72"><data value='test_corrupted_file_handling'>TestFileOperationExceptions.test_corrupted_file_handling</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t83">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t83"><data value='test_empty_file_handling'>TestFileOperationExceptions.test_empty_file_handling</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t92">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t92"><data value='test_very_large_file_handling'>TestFileOperationExceptions.test_very_large_file_handling</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t106">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t106"><data value='test_file_with_unicode_content'>TestFileOperationExceptions.test_file_with_unicode_content</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t132">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t132"><data value='temp_dir'>TestConfigurationExceptions.temp_dir</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t137">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t137"><data value='test_missing_config_file'>TestConfigurationExceptions.test_missing_config_file</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t142">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t142"><data value='test_malformed_config_file'>TestConfigurationExceptions.test_malformed_config_file</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t150">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t150"><data value='test_missing_required_parameters'>TestConfigurationExceptions.test_missing_required_parameters</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t161">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t161"><data value='test_invalid_parameter_types'>TestConfigurationExceptions.test_invalid_parameter_types</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t175">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t175"><data value='test_config_with_nonexistent_file_path'>TestConfigurationExceptions.test_config_with_nonexistent_file_path</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t194">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t194"><data value='test_connection_timeout'>TestNetworkExceptions.test_connection_timeout</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t202">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t202"><data value='test_connection_refused'>TestNetworkExceptions.test_connection_refused</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t210">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t210"><data value='test_invalid_hostname'>TestNetworkExceptions.test_invalid_hostname</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t217">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t217"><data value='test_ssl_connection_errors'>TestNetworkExceptions.test_ssl_connection_errors</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t226">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t226"><data value='test_socket_creation_failure'>TestNetworkExceptions.test_socket_creation_failure</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t235">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t235"><data value='test_send_failure'>TestNetworkExceptions.test_send_failure</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t246">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t246"><data value='test_receive_failure'>TestNetworkExceptions.test_receive_failure</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t261">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t261"><data value='temp_dir'>TestMalformedInputHandling.temp_dir</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t267">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t267"><data value='test_server'>TestMalformedInputHandling.test_server</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t301">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t301"><data value='test_oversized_query'>TestMalformedInputHandling.test_oversized_query</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t319">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t319"><data value='test_binary_query_data'>TestMalformedInputHandling.test_binary_query_data</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t337">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t337"><data value='test_empty_query'>TestMalformedInputHandling.test_empty_query</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t352">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t352"><data value='test_query_with_newlines'>TestMalformedInputHandling.test_query_with_newlines</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t365">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t365"><data value='test_unicode_query_handling'>TestMalformedInputHandling.test_unicode_query_handling</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>52</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="52 52">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t23">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t23"><data value='temp_test_file'>temp_test_file</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t37">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t37"><data value='test_create_test_queries'>test_create_test_queries</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t47">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t47"><data value='test_benchmark_algorithm'>test_benchmark_algorithm</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t61">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t61"><data value='test_plot_comparison'>test_plot_comparison</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t80">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t80"><data value='test_generate_report'>test_generate_report</data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t92">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t92"><data value='benchmark_side_effect'>test_generate_report.benchmark_side_effect</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t140">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t140"><data value='test_create_test_queries_with_empty_file'>test_create_test_queries_with_empty_file</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t157">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t157"><data value='test_generate_report_skips_missing_files'>test_generate_report_skips_missing_files</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t172">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t172"><data value='test_benchmark_algorithm_reread_mode'>test_benchmark_algorithm_reread_mode</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t199">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t199"><data value='test_plot_comparison_empty_data'>test_plot_comparison_empty_data</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t214">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t214"><data value='test_plot_comparison_no_algorithms'>test_plot_comparison_no_algorithms</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t233">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t233"><data value='test_main_execution'>test_main_execution</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t255">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t255"><data value='test_plot_comparison_with_empty_dataframe'>test_plot_comparison_with_empty_dataframe</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t264">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t264"><data value='test_benchmark_algorithm_average_calculation'>test_benchmark_algorithm_average_calculation</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t267">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t267"><data value='init__'>test_benchmark_algorithm_average_calculation.FakeAlgo.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t269">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t269"><data value='load'>test_benchmark_algorithm_average_calculation.FakeAlgo.load</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t270">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t270"><data value='search'>test_benchmark_algorithm_average_calculation.FakeAlgo.search</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t271">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t271"><data value='name'>test_benchmark_algorithm_average_calculation.FakeAlgo.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_hashset_py.html#t22">tests/test_hashset.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_hashset_py.html#t22"><data value='test_search_speed'>test_search_speed</data></a></td>
                <td>24</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="13 24">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_hashset_py.html">tests/test_hashset.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_hashset_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t39">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t39"><data value='init__'>LoadTestResult.__init__</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t51">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t51"><data value='add_result'>LoadTestResult.add_result</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t64">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t64"><data value='calculate_stats'>LoadTestResult.calculate_stats</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t91">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t91"><data value='create_test_file_for_load'>create_test_file_for_load</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t105">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t105"><data value='create_load_test_config'>create_load_test_config</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t130">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t130"><data value='worker_function'>worker_function</data></a></td>
                <td>16</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="12 16">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t159">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t159"><data value='temp_dir'>TestLoadTesting.temp_dir</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t165">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t165"><data value='test_server'>TestLoadTesting.test_server</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t191">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t191"><data value='test_concurrent_connections_scaling'>TestLoadTesting.test_concurrent_connections_scaling</data></a></td>
                <td>43</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="40 43">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t284">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t284"><data value='test_sustained_load'>TestLoadTesting.test_sustained_load</data></a></td>
                <td>46</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="45 46">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t306">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t306"><data value='monitor_resources'>TestLoadTesting.test_sustained_load.monitor_resources</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t328">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t328"><data value='sustained_worker'>TestLoadTesting.test_sustained_load.sustained_worker</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t407">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t407"><data value='test_connection_limit'>TestLoadTesting.test_connection_limit</data></a></td>
                <td>50</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="41 50">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t39">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t39"><data value='init__'>PerformanceMonitor.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t45">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t45"><data value='start_monitoring'>PerformanceMonitor.start_monitoring</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t54">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t54"><data value='stop_monitoring'>PerformanceMonitor.stop_monitoring</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t80">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t80"><data value='monitor_loop'>PerformanceMonitor._monitor_loop</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t99">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t99"><data value='create_test_file'>create_test_file</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t116">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t116"><data value='create_temp_config'>create_temp_config</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t145">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t145"><data value='temp_dir'>TestPerformanceScaling.temp_dir</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t150">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t150"><data value='test_file_size_scaling_performance'>TestPerformanceScaling.test_file_size_scaling_performance</data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t238">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t238"><data value='test_memory_usage_scaling'>TestPerformanceScaling.test_memory_usage_scaling</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t274">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t274"><data value='test_large_file_performance'>TestPerformanceScaling.test_large_file_performance</data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t322">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t322"><data value='test_query_pattern_performance'>TestPerformanceScaling.test_query_pattern_performance</data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t371">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t371"><data value='temp_dir'>TestPerformanceDegradation.temp_dir</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t376">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t376"><data value='test_find_breaking_point'>TestPerformanceDegradation.test_find_breaking_point</data></a></td>
                <td>37</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="26 37">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t28">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t28"><data value='test_file'>test_file</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t46">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t46"><data value='test_file_not_found'>test_file_not_found</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t51">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t51"><data value='test_search_cached'>test_search_cached</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t58">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t58"><data value='test_search_reread'>test_search_reread</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t65">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t65"><data value='test_file_modification'>test_file_modification</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t93">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t93"><data value='test_empty_file'>test_empty_file</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t102">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t102"><data value='test_large_file'>test_large_file</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t115">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html#t115"><data value='test_unicode_handling'>test_unicode_handling</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t19">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t19"><data value='test_initialization'>TestHashSetSearch.test_initialization</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t25">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t25"><data value='test_load_file'>TestHashSetSearch.test_load_file</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t42">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t42"><data value='test_search_existing_string'>TestHashSetSearch.test_search_existing_string</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t57">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t57"><data value='test_search_non_existing_string'>TestHashSetSearch.test_search_non_existing_string</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t71">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t71"><data value='test_search_without_load'>TestHashSetSearch.test_search_without_load</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t81">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t81"><data value='test_initialization'>TestLinearSearch.test_initialization</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t87">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t87"><data value='test_load_and_search'>TestLinearSearch.test_load_and_search</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t105">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t105"><data value='test_initialization'>TestBinarySearch.test_initialization</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t111">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t111"><data value='test_load_and_search'>TestBinarySearch.test_load_and_search</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t129">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t129"><data value='test_initialization'>TestMMapSearch.test_initialization</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t135">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t135"><data value='test_load_and_search'>TestMMapSearch.test_load_and_search</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t149">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t149"><data value='test_search_without_load'>TestMMapSearch.test_search_without_load</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t159">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t159"><data value='test_initialization'>TestGrepSearch.test_initialization</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t165">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t165"><data value='test_search_found'>TestGrepSearch.test_search_found</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t177">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t177"><data value='test_search_not_found'>TestGrepSearch.test_search_not_found</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t188">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t188"><data value='test_search_error'>TestGrepSearch.test_search_error</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t198">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t198"><data value='test_search_without_load'>TestGrepSearch.test_search_without_load</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t208">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t208"><data value='test_initialization'>TestHashSetSearchReread.test_initialization</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t214">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t214"><data value='test_load_and_search'>TestHashSetSearchReread.test_load_and_search</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t228">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t228"><data value='test_search_without_file_path'>TestHashSetSearchReread.test_search_without_file_path</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t238">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t238"><data value='test_all_algorithms_implement_interface'>TestSearchAlgorithmInterface.test_all_algorithms_implement_interface</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t263">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t263"><data value='test_empty_file'>TestEdgeCases.test_empty_file</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t277">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t277"><data value='test_platform_specific_code_coverage'>TestEdgeCases.test_platform_specific_code_coverage</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t297">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t297"><data value='test_import_error_handling'>TestEdgeCases.test_import_error_handling</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t305">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t305"><data value='test_hashset_search_fast_load_with_reread'>TestEdgeCases.test_hashset_search_fast_load_with_reread</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t329">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t329"><data value='test_hashset_search_simple_search_method'>TestEdgeCases.test_hashset_search_simple_search_method</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t357">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t357"><data value='test_hashset_search_exception_handling'>TestEdgeCases.test_hashset_search_exception_handling</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t365">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t365"><data value='test_mmap_search_with_index'>TestEdgeCases.test_mmap_search_with_index</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t386">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t386"><data value='test_mmap_search_without_index'>TestEdgeCases.test_mmap_search_without_index</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t407">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t407"><data value='test_grep_search_with_parallel'>TestEdgeCases.test_grep_search_with_parallel</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t431">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t431"><data value='test_binary_search_without_deduplication'>TestEdgeCases.test_binary_search_without_deduplication</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t455">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t455"><data value='test_single_line_file'>TestEdgeCases.test_single_line_file</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t471">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t471"><data value='test_unicode_content'>TestEdgeCases.test_unicode_content</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="50 51">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t49">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t49"><data value='real_config'>real_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t56">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t56"><data value='test_config_file_not_found'>TestServerConfig.test_config_file_not_found</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t61">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t61"><data value='test_config_parse_error'>TestServerConfig.test_config_parse_error</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t73">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t73"><data value='test_config_section_exception_handling'>TestServerConfig.test_config_section_exception_handling</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t95">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t95"><data value='test_config_type_validation_errors'>TestServerConfig.test_config_type_validation_errors</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t117">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t117"><data value='test_file_not_readable'>TestServerConfig.test_file_not_readable</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t144">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t144"><data value='test_config_required_parameter_missing'>TestServerConfig.test_config_required_parameter_missing</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t162">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t162"><data value='test_config_get_with_section_exception'>TestServerConfig.test_config_get_with_section_exception</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t182">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t182"><data value='test_valid_configuration'>TestServerConfig.test_valid_configuration</data></a></td>
                <td>6</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="1 6">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t191">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t191"><data value='test_get_methods'>TestServerConfig.test_get_methods</data></a></td>
                <td>7</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="1 7">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t208">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t208"><data value='test_file_not_found'>TestFileSearchEngine.test_file_not_found</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t212">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t212"><data value='test_search_engine_initialization'>TestFileSearchEngine.test_search_engine_initialization</data></a></td>
                <td>6</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="1 6">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t221">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t221"><data value='test_search_without_reread'>TestFileSearchEngine.test_search_without_reread</data></a></td>
                <td>9</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="1 9">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t233">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t233"><data value='test_search_with_reread'>TestFileSearchEngine.test_search_with_reread</data></a></td>
                <td>9</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="1 9">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t246">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t246"><data value='test_search_exception_handling'>TestFileSearchEngine.test_search_exception_handling</data></a></td>
                <td>7</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="1 7">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t261">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t261"><data value='test_setup_logging_with_log_dir'>TestSetupLogging.test_setup_logging_with_log_dir</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t269">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t269"><data value='test_setup_logging_without_log_dir'>TestSetupLogging.test_setup_logging_without_log_dir</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t278">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t278"><data value='test_setup_logging_default_file'>TestSetupLogging.test_setup_logging_default_file</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t288">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t288"><data value='create_mock_handler'>TestStringSearchHandler.create_mock_handler</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t311">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t311"><data value='test_handler_empty_request'>TestStringSearchHandler.test_handler_empty_request</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t329">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t329"><data value='test_handler_empty_query_after_processing'>TestStringSearchHandler.test_handler_empty_query_after_processing</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t348">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t348"><data value='test_handler_no_server_config'>TestStringSearchHandler.test_handler_no_server_config</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t374">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t374"><data value='create_mock_config'>TestStringSearchServer.create_mock_config</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t390">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t390"><data value='create_mock_search_engine'>TestStringSearchServer.create_mock_search_engine</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t398">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t398"><data value='test_server_initialization'>TestStringSearchServer.test_server_initialization</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t414">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t414"><data value='test_server_socket_options'>TestStringSearchServer.test_server_socket_options</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t431">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t431"><data value='test_server_ipv6_binding'>TestStringSearchServer.test_server_ipv6_binding</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t449">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t449"><data value='test_handle_error'>TestStringSearchServer.test_handle_error</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t461">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t461"><data value='test_shutdown_request'>TestStringSearchServer.test_shutdown_request</data></a></td>
                <td>9</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="4 9">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t481">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t481"><data value='create_mock_config'>TestCreateSSLContext.create_mock_config</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t500">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t500"><data value='test_ssl_disabled'>TestCreateSSLContext.test_ssl_disabled</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t506">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t506"><data value='test_ssl_cert_file_not_found'>TestCreateSSLContext.test_ssl_cert_file_not_found</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t513">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t513"><data value='test_ssl_key_file_not_found'>TestCreateSSLContext.test_ssl_key_file_not_found</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t528">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t528"><data value='test_ssl_context_creation_success'>TestCreateSSLContext.test_ssl_context_creation_success</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t542">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t542"><data value='test_ssl_context_tls_versions'>TestCreateSSLContext.test_ssl_context_tls_versions</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t560">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t560"><data value='test_ssl_context_client_verification'>TestCreateSSLContext.test_ssl_context_client_verification</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t574">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t574"><data value='test_ssl_context_creation_failure'>TestCreateSSLContext.test_ssl_context_creation_failure</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t588">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t588"><data value='temp_config_and_file'>TestIntegration.temp_config_and_file</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t611">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t611"><data value='test_full_server_lifecycle'>TestIntegration.test_full_server_lifecycle</data></a></td>
                <td>30</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="2 30">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t645">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t645"><data value='test_connection'>TestIntegration.test_full_server_lifecycle.test_connection</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t673">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t673"><data value='test_reread_on_query_mode'>TestIntegration.test_reread_on_query_mode</data></a></td>
                <td>29</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="5 29">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t726">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t726"><data value='test_server_bind_error'>TestErrorHandling.test_server_bind_error</data></a></td>
                <td>5</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="2 5">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t738">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t738"><data value='test_socket_timeout_handling'>TestErrorHandling.test_socket_timeout_handling</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t757">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t757"><data value='test_large_payload_handling'>TestErrorHandling.test_large_payload_handling</data></a></td>
                <td>21</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="5 21">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t792">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t792"><data value='create_temp_files'>TestErrorHandling.create_temp_files</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t821">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t821"><data value='test_logging_levels'>TestLoggingConfiguration.test_logging_levels</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t834">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t834"><data value='test_logging_format'>TestLoggingConfiguration.test_logging_format</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t843">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t843"><data value='test_logging_handlers'>TestLoggingConfiguration.test_logging_handlers</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t855">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t855"><data value='test_socket_buffer_configuration'>TestPerformanceOptimizations.test_socket_buffer_configuration</data></a></td>
                <td>12</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="5 12">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t882">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t882"><data value='test_tcp_nodelay_configuration'>TestPerformanceOptimizations.test_tcp_nodelay_configuration</data></a></td>
                <td>11</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="5 11">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t906">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t906"><data value='test_keepalive_configuration'>TestPerformanceOptimizations.test_keepalive_configuration</data></a></td>
                <td>8</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="2 8">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t925">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t925"><data value='create_temp_files'>TestPerformanceOptimizations.create_temp_files</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t946">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t946"><data value='test_concurrent_request_handling'>TestThreadingBehavior.test_concurrent_request_handling</data></a></td>
                <td>26</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="2 26">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t966">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t966"><data value='make_request'>TestThreadingBehavior.test_concurrent_request_handling.make_request</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t997">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t997"><data value='test_thread_safety'>TestThreadingBehavior.test_thread_safety</data></a></td>
                <td>16</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="2 16">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1007">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1007"><data value='search_worker'>TestThreadingBehavior.test_thread_safety.search_worker</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1031">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1031"><data value='create_temp_files'>TestThreadingBehavior.create_temp_files</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1052">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1052"><data value='test_empty_search_file'>TestEdgeCases.test_empty_search_file</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1074">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1074"><data value='test_very_long_query'>TestEdgeCases.test_very_long_query</data></a></td>
                <td>18</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="2 18">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1103">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1103"><data value='test_special_characters_in_query'>TestEdgeCases.test_special_characters_in_query</data></a></td>
                <td>24</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="5 24">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1148">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1148"><data value='test_binary_data_handling'>TestEdgeCases.test_binary_data_handling</data></a></td>
                <td>18</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="2 18">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1177">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1177"><data value='create_temp_files'>TestEdgeCases.create_temp_files</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1204">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1204"><data value='test_main_function_normal_execution'>TestMainFunction.test_main_function_normal_execution</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1246">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1246"><data value='test_main_function_startup_exception'>TestMainFunction.test_main_function_startup_exception</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>126</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="125 126">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t20">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t20"><data value='print_header'>print_header</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t27">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t27"><data value='print_status'>print_status</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t33">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t33"><data value='check_python_version'>check_python_version</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t49">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t49"><data value='check_core_modules'>check_core_modules</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t74">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t74"><data value='check_dependencies'>check_dependencies</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t118">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t118"><data value='check_configuration'>check_configuration</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t138">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t138"><data value='check_ssl_certificates'>check_ssl_certificates</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t161">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t161"><data value='check_test_data'>check_test_data</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t179">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t179"><data value='check_executable_permissions'>check_executable_permissions</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t206">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t206"><data value='run_basic_functionality_test'>run_basic_functionality_test</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t233">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t233"><data value='run_type_checking'>run_type_checking</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t263">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t263"><data value='generate_summary_report'>generate_summary_report</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html#t287">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html#t287"><data value='main'>main</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>4192</td>
                <td>1660</td>
                <td>0</td>
                <td class="right" data-ratio="2532 4192">60%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 22:11 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
