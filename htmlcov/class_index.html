<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">60%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 22:11 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="client_py.html#t24">client.py</a></td>
                <td class="name left"><a href="client_py.html#t24"><data value='SearchClient'>SearchClient</data></a></td>
                <td>141</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="72 141">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="client_py.html">client.py</a></td>
                <td class="name left"><a href="client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>73</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="66 73">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t15">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t15"><data value='DefaultConfig'>DefaultConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t21">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t21"><data value='ServerConfig'>ServerConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t32">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t32"><data value='SSLConfig'>SSLConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t42">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t42"><data value='LoggingConfig'>LoggingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t49">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t49"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="64 75">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_wrappers_py.html">create_wrappers.py</a></td>
                <td class="name left"><a href="create_wrappers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_certs_py.html">generate_certs.py</a></td>
                <td class="name left"><a href="generate_certs_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html#t49">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html#t49"><data value='PerformanceReportGenerator'>PerformanceReportGenerator</data></a></td>
                <td>277</td>
                <td>277</td>
                <td>0</td>
                <td class="right" data-ratio="0 277">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_performance_report_py.html">generate_performance_report.py</a></td>
                <td class="name left"><a href="generate_performance_report_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html#t39">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html#t39"><data value='SearchAlgorithm'>SearchAlgorithm</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="generate_speed_report_py.html">generate_speed_report.py</a></td>
                <td class="name left"><a href="generate_speed_report_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>125</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="120 125">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html#t31">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html#t31"><data value='PerformanceTestClient'>PerformanceTestClient</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="performance_test_client_py.html">performance_test_client.py</a></td>
                <td class="name left"><a href="performance_test_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="run_comprehensive_tests_py.html">run_comprehensive_tests.py</a></td>
                <td class="name left"><a href="run_comprehensive_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t32">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t32"><data value='SearchAlgorithm'>SearchAlgorithm</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t80">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t80"><data value='HashSetSearch'>HashSetSearch</data></a></td>
                <td>101</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="77 101">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t356">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t356"><data value='LinearSearch'>LinearSearch</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t416">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t416"><data value='BinarySearch'>BinarySearch</data></a></td>
                <td>14</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="13 14">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t486">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t486"><data value='MMapSearch'>MMapSearch</data></a></td>
                <td>42</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="41 42">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html#t597">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html#t597"><data value='GrepSearch'>GrepSearch</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="search_algorithms_new_py.html">search_algorithms_new.py</a></td>
                <td class="name left"><a href="search_algorithms_new_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="46 51">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t27">server.py</a></td>
                <td class="name left"><a href="server_py.html#t27"><data value='FileSearchError'>FileSearchError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t36">server.py</a></td>
                <td class="name left"><a href="server_py.html#t36"><data value='FileSearchEngine'>FileSearchEngine</data></a></td>
                <td>17</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="15 17">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t125">server.py</a></td>
                <td class="name left"><a href="server_py.html#t125"><data value='StringSearchHandler'>StringSearchHandler</data></a></td>
                <td>31</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="22 31">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html#t205">server.py</a></td>
                <td class="name left"><a href="server_py.html#t205"><data value='StringSearchServer'>StringSearchServer</data></a></td>
                <td>31</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="27 31">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="server_py.html">server.py</a></td>
                <td class="name left"><a href="server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>109</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="63 109">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_client_config_py.html">test_client_config.py</a></td>
                <td class="name left"><a href="test_client_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="34 45">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="test_installations_py.html">test_installations.py</a></td>
                <td class="name left"><a href="test_installations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>169</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="59 169">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html">tests/__init__.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t16">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t16"><data value='TestSearchClient'>TestSearchClient</data></a></td>
                <td>128</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="122 128">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t255">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html#t255"><data value='TestMainFunction'>TestMainFunction</data></a></td>
                <td>60</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="44 60">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html">tests/test_client.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>75</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="74 75">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html">tests/test_config.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t38">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t38"><data value='TestFileOperationExceptions'>TestFileOperationExceptions</data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t128">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t128"><data value='TestConfigurationExceptions'>TestConfigurationExceptions</data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t191">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t191"><data value='TestNetworkExceptions'>TestNetworkExceptions</data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t257">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html#t257"><data value='TestMalformedInputHandling'>TestMalformedInputHandling</data></a></td>
                <td>53</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="49 53">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html">tests/test_exception_handling.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_exception_handling_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="52 52">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t266">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html#t266"><data value='FakeAlgo'>test_benchmark_algorithm_average_calculation.FakeAlgo</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html">tests/test_generate_speed_report.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_generate_speed_report_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>156</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="154 156">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_hashset_py.html">tests/test_hashset.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_hashset_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="17 29">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t36">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t36"><data value='LoadTestResult'>LoadTestResult</data></a></td>
                <td>26</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="22 26">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t155">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html#t155"><data value='TestLoadTesting'>TestLoadTesting</data></a></td>
                <td>177</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="160 177">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html">tests/test_load_testing.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_load_testing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="53 57">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t36">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t36"><data value='PerformanceMonitor'>PerformanceMonitor</data></a></td>
                <td>26</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="23 26">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t141">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t141"><data value='TestPerformanceScaling'>TestPerformanceScaling</data></a></td>
                <td>100</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="100 100">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t367">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html#t367"><data value='TestPerformanceDegradation'>TestPerformanceDegradation</data></a></td>
                <td>39</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="28 39">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html">tests/test_performance_comprehensive.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_performance_comprehensive_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="46 46">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html">tests/test_search.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t16">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t16"><data value='TestHashSetSearch'>TestHashSetSearch</data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t78">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t78"><data value='TestLinearSearch'>TestLinearSearch</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t102">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t102"><data value='TestBinarySearch'>TestBinarySearch</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t126">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t126"><data value='TestMMapSearch'>TestMMapSearch</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t156">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t156"><data value='TestGrepSearch'>TestGrepSearch</data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t205">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t205"><data value='TestHashSetSearchReread'>TestHashSetSearchReread</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t235">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t235"><data value='TestSearchAlgorithmInterface'>TestSearchAlgorithmInterface</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t260">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html#t260"><data value='TestEdgeCases'>TestEdgeCases</data></a></td>
                <td>120</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="117 120">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html">tests/test_search_algorithms.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_search_algorithms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="50 51">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t53">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t53"><data value='TestServerConfig'>TestServerConfig</data></a></td>
                <td>66</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="49 66">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t205">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t205"><data value='TestFileSearchEngine'>TestFileSearchEngine</data></a></td>
                <td>33</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="6 33">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t256">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t256"><data value='TestSetupLogging'>TestSetupLogging</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t285">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t285"><data value='TestStringSearchHandler'>TestStringSearchHandler</data></a></td>
                <td>48</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="35 48">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t371">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t371"><data value='TestStringSearchServer'>TestStringSearchServer</data></a></td>
                <td>50</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="36 50">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t478">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t478"><data value='TestCreateSSLContext'>TestCreateSSLContext</data></a></td>
                <td>42</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="41 42">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t584">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t584"><data value='TestIntegration'>TestIntegration</data></a></td>
                <td>68</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="12 68">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t723">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t723"><data value='TestErrorHandling'>TestErrorHandling</data></a></td>
                <td>40</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="17 40">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t810">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t810"><data value='TestCommandLineArguments'>TestCommandLineArguments</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t816">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t816"><data value='TestLoggingConfiguration'>TestLoggingConfiguration</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t852">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t852"><data value='TestPerformanceOptimizations'>TestPerformanceOptimizations</data></a></td>
                <td>36</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="17 36">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t943">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t943"><data value='TestThreadingBehavior'>TestThreadingBehavior</data></a></td>
                <td>56</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="9 56">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1049">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1049"><data value='TestEdgeCases'>TestEdgeCases</data></a></td>
                <td>73</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="19 73">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1195">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html#t1195"><data value='TestMainFunction'>TestMainFunction</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html">tests/test_server.py</a></td>
                <td class="name left"><a href="z_a44f0ac069e85531_test_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>127</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="125 127">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="verify_installation_py.html">verify_installation.py</a></td>
                <td class="name left"><a href="verify_installation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>165</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="0 165">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>4192</td>
                <td>1660</td>
                <td>0</td>
                <td class="right" data-ratio="2532 4192">60%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 22:11 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
