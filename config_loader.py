#!/usr/bin/env python3
"""
Centralized configuration loader for TCP String Search Server.

This module provides statically typed configuration loading using TypedDict
and configparser, following best practices for type safety and validation.
"""

import os
import configparser
from configparser import ConfigParser
from pathlib import Path
from typing import TypedDict, Optional


class DefaultConfig(TypedDict):
    """Configuration from DEFAULT section."""
    linuxpath: str
    reread_on_query: bool


class ServerConfig(TypedDict):
    """Configuration from server section."""
    host: str
    port: int
    max_connections: int
    max_payload_size: int
    connection_timeout: int
    tcp_nodelay: bool
    socket_buffer_size: int


class SSLConfig(TypedDict):
    """Configuration from ssl section."""
    ssl_enabled: bool
    ssl_cert_file: str
    ssl_key_file: str
    min_tls_version: str
    verify_client_cert: bool
    ca_file: str


class LoggingConfig(TypedDict):
    """Configuration from logging section."""
    log_level: str
    log_file: str
    thread_pool_size: int


class ConfigurationError(Exception):
    """Raised when configuration is invalid or missing."""
    pass


def find_config_file() -> str:
    """
    Find configuration file in standard locations.

    Returns:
        Path to the first config file found

    Raises:
        ConfigurationError: If no config file is found
    """
    config_locations = [
        "config.ini",
        "~/.local/tcp-string-search/config.ini"
    ]

    for location in config_locations:
        config_path = Path(location).expanduser()
        if config_path.exists():
            return str(config_path)

    raise ConfigurationError(
        f"No configuration file found in any of these locations: {config_locations}"
    )


def load_config(config_file: Optional[str] = None) -> tuple[DefaultConfig, ServerConfig, SSLConfig, LoggingConfig]:
    """
    Load configuration from config.ini file with proper static typing.

    Args:
        config_file: Path to configuration file. If None, searches standard locations.

    Returns:
        Tuple containing (default_config, server_config, ssl_config, logging_config)

    Raises:
        ConfigurationError: If config file is invalid or missing required parameters
    """
    if config_file is None:
        config_file = find_config_file()
    
    if not os.path.exists(config_file):
        raise ConfigurationError(f"Configuration file {config_file} not found")

    parser = ConfigParser()
    
    try:
        parser.read(config_file)
    except Exception as e:
        raise ConfigurationError(f"Failed to parse configuration file: {e}")

    # Load DEFAULT configuration with error handling
    try:
        default_config: DefaultConfig = {
            "linuxpath": parser.get("DEFAULT", "linuxpath"),
            "reread_on_query": parser.getboolean("DEFAULT", "reread_on_query"),
        }
    except (configparser.NoOptionError, ValueError) as e:
        raise ConfigurationError(f"Invalid or missing required parameter in DEFAULT section: {e}")

    # Load server configuration
    server_section = parser['server'] if 'server' in parser else parser['DEFAULT']
    server_config: ServerConfig = {
        "host": server_section.get("host", "localhost"),
        "port": server_section.getint("port", 8888),
        "max_connections": server_section.getint("max_connections", 200),
        "max_payload_size": server_section.getint("max_payload_size", 1024),
        "connection_timeout": server_section.getint("connection_timeout", 5),
        "tcp_nodelay": server_section.getboolean("tcp_nodelay", True),
        "socket_buffer_size": server_section.getint("socket_buffer_size", 262144),
    }

    # Load SSL configuration
    ssl_section = parser['ssl'] if 'ssl' in parser else parser['DEFAULT']
    
    # Resolve certificate paths relative to config directory
    config_dir = Path(config_file).parent.resolve()
    
    def resolve_cert_path(cert_path: str) -> str:
        if not cert_path:
            return cert_path
        cert_path_obj = Path(cert_path)
        if cert_path_obj.is_absolute():
            return str(cert_path_obj)
        else:
            return str(config_dir / cert_path)

    ssl_config: SSLConfig = {
        "ssl_enabled": ssl_section.getboolean("ssl_enabled", False),
        "ssl_cert_file": resolve_cert_path(ssl_section.get("ssl_cert_file", "certs/server.crt")),
        "ssl_key_file": resolve_cert_path(ssl_section.get("ssl_key_file", "certs/server.key")),
        "min_tls_version": ssl_section.get("min_tls_version", "TLSv1.3"),
        "verify_client_cert": ssl_section.getboolean("verify_client_cert", False),
        "ca_file": resolve_cert_path(ssl_section.get("ca_file", "certs/ca.crt")),
    }

    # Load logging configuration
    logging_section = parser['logging'] if 'logging' in parser else parser['DEFAULT']
    logging_config: LoggingConfig = {
        "log_level": logging_section.get("log_level", "DEBUG"),
        "log_file": logging_section.get("log_file", "logs/server.log"),
        "thread_pool_size": logging_section.getint("thread_pool_size", 50),
    }

    # Validate that the file specified by linuxpath exists
    file_path = default_config["linuxpath"]
    if not Path(file_path).is_absolute():
        file_path = str(config_dir / file_path)
    
    if not os.path.exists(file_path):
        raise ConfigurationError(f"File specified by linuxpath does not exist: {file_path}")

    return default_config, server_config, ssl_config, logging_config


def get_file_path(config_file: Optional[str] = None) -> str:
    """
    Get the resolved file path from configuration.

    Args:
        config_file: Path to configuration file

    Returns:
        Absolute path to the file specified by linuxpath
    """
    default_config, _, _, _ = load_config(config_file)
    
    if config_file is None:
        config_file = find_config_file()
    
    config_dir = Path(config_file).parent.resolve()
    file_path = default_config["linuxpath"]
    
    if not Path(file_path).is_absolute():
        file_path = str(config_dir / file_path)
    
    return file_path
