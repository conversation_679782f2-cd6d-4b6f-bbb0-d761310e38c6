#!/usr/bin/env python3
"""
Simple test runner to check if tests can be executed.
"""

import sys
import os
import subprocess

def main():
    print("TCP String Search Server - Test Runner")
    print("=" * 50)
    
    # Change to the correct directory
    os.chdir('/home/<USER>/CODES/interview')
    print(f"Working directory: {os.getcwd()}")
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check if pytest is available
    try:
        import pytest
        print(f"pytest version: {pytest.__version__}")
    except ImportError:
        print("ERROR: pytest not available")
        return 1
    
    # Try to import our modules
    print("\nTesting imports...")
    try:
        import config_loader
        print("✓ config_loader imported")
    except Exception as e:
        print(f"✗ config_loader import failed: {e}")
        return 1
    
    try:
        import server
        print("✓ server imported")
    except Exception as e:
        print(f"✗ server import failed: {e}")
        return 1
    
    try:
        import client
        print("✓ client imported")
    except Exception as e:
        print(f"✗ client import failed: {e}")
        return 1
    
    try:
        import search_algorithms_new
        print("✓ search_algorithms_new imported")
    except Exception as e:
        print(f"✗ search_algorithms_new import failed: {e}")
        return 1
    
    # List test files
    print("\nAvailable test files:")
    test_files = []
    for file in os.listdir('tests'):
        if file.startswith('test_') and file.endswith('.py'):
            test_files.append(file)
            print(f"  - {file}")
    
    if not test_files:
        print("No test files found!")
        return 1
    
    # Try to run a simple test
    print("\nRunning a simple test...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'tests/test_config.py::test_config_loading', 
            '-v', '--tb=short'
        ], capture_output=True, text=True, timeout=30)
        
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("Test execution timed out")
        return 1
    except Exception as e:
        print(f"Test execution failed: {e}")
        return 1
    
    print("\nTest runner completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
