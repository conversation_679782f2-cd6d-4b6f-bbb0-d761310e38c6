#!/usr/bin/env python3
"""
Modern test suite for server functionality using the new configuration system.

This module tests all server components with the TypedDict-based configuration
and covers all 4 configuration scenarios: SSL on/off × REREAD_ON_QUERY on/off.
"""

import os
import sys
import tempfile
import threading
import time
import socket
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from config_loader import (
    load_config, ConfigurationError, DefaultConfig, 
    ServerConfig, SSLConfig, LoggingConfig
)
from server import (
    FileSearchEngine, StringSearchServer, StringSearchHandler,
    create_ssl_context, FileSearchError, setup_logging
)


class TestConfigurationSystem:
    """Test the new TypedDict-based configuration system."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("line1\nline2\nline3\n")
        return test_file

    def create_config(self, temp_dir: Path, test_file: Path, **overrides) -> Path:
        """Create a configuration file with optional overrides."""
        defaults = {
            'linuxpath': str(test_file),
            'reread_on_query': 'false',
            'host': 'localhost',
            'port': '0',
            'ssl_enabled': 'false'
        }
        defaults.update(overrides)
        
        config_file = temp_dir / "config.ini"
        config_content = f"""[DEFAULT]
linuxpath = {defaults['linuxpath']}
reread_on_query = {defaults['reread_on_query']}

[server]
host = {defaults['host']}
port = {defaults['port']}
max_connections = 100
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[ssl]
ssl_enabled = {defaults['ssl_enabled']}
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
min_tls_version = TLSv1.3
verify_client_cert = false
ca_file = certs/ca.crt

[logging]
log_level = DEBUG
log_file = logs/server.log
thread_pool_size = 50
"""
        config_file.write_text(config_content)
        return config_file

    def test_configuration_loading_success(self, temp_dir: Path, test_file: Path):
        """Test successful configuration loading."""
        config_file = self.create_config(temp_dir, test_file)
        
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        # Verify types and values
        assert isinstance(default_config, dict)
        assert isinstance(server_config, dict)
        assert isinstance(ssl_config, dict)
        assert isinstance(logging_config, dict)
        
        assert default_config["linuxpath"] == str(test_file)
        assert default_config["reread_on_query"] is False
        assert server_config["host"] == "localhost"
        assert server_config["port"] == 0
        assert ssl_config["ssl_enabled"] is False

    def test_configuration_error_handling(self, temp_dir: Path):
        """Test configuration error handling."""
        # Test missing file
        with pytest.raises(ConfigurationError):
            load_config("nonexistent.ini")
        
        # Test missing required parameter
        config_file = temp_dir / "incomplete.ini"
        config_file.write_text("""[DEFAULT]
# Missing linuxpath
reread_on_query = false
""")
        
        with pytest.raises(ConfigurationError):
            load_config(str(config_file))

    def test_configuration_type_validation(self, temp_dir: Path, test_file: Path):
        """Test configuration type validation."""
        config_file = self.create_config(temp_dir, test_file, reread_on_query="invalid_boolean")
        
        with pytest.raises(ConfigurationError):
            load_config(str(config_file))


class TestFileSearchEngine:
    """Test FileSearchEngine with new configuration system."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\ntest line 3\n")
        return test_file

    def test_search_engine_cached_mode(self, test_file: Path):
        """Test search engine in cached mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=False)
        
        assert engine.file_path == str(test_file)
        assert engine.reread_on_query is False
        
        # Test search functionality
        assert engine.search("test line 1") is True
        assert engine.search("nonexistent line") is False

    def test_search_engine_reread_mode(self, test_file: Path):
        """Test search engine in reread mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=True)
        
        assert engine.file_path == str(test_file)
        assert engine.reread_on_query is True
        
        # Test search functionality
        assert engine.search("test line 2") is True
        assert engine.search("nonexistent line") is False

    def test_search_performance_requirements(self, test_file: Path):
        """Test that search performance meets requirements."""
        # Test cached mode (≤0.5ms)
        engine_cached = FileSearchEngine(str(test_file), reread_on_query=False)
        start_time = time.perf_counter()
        result = engine_cached.search("test line 1")
        end_time = time.perf_counter()
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        assert execution_time_ms <= 0.5
        
        # Test reread mode (≤40ms)
        engine_reread = FileSearchEngine(str(test_file), reread_on_query=True)
        start_time = time.perf_counter()
        result = engine_reread.search("test line 1")
        end_time = time.perf_counter()
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        assert execution_time_ms <= 40.0

    def test_search_engine_file_not_found(self):
        """Test search engine with non-existent file."""
        with pytest.raises(FileSearchError):
            FileSearchEngine("/nonexistent/file.txt", reread_on_query=False)


class TestStringSearchServer:
    """Test StringSearchServer with new configuration system."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\ntest line 3\n")
        return test_file

    def create_config_components(self, temp_dir: Path, test_file: Path, ssl_enabled: bool = False, reread_on_query: bool = False):
        """Create configuration components."""
        config_file = temp_dir / "config.ini"
        config_content = f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = {str(reread_on_query).lower()}

[server]
host = localhost
port = 0
max_connections = 100
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[ssl]
ssl_enabled = {str(ssl_enabled).lower()}
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
min_tls_version = TLSv1.3
verify_client_cert = false
ca_file = certs/ca.crt

[logging]
log_level = DEBUG
log_file = logs/server.log
thread_pool_size = 50
"""
        config_file.write_text(config_content)
        return load_config(str(config_file))

    def test_server_initialization(self, temp_dir: Path, test_file: Path):
        """Test server initialization with new configuration system."""
        default_config, server_config, ssl_config, logging_config = self.create_config_components(temp_dir, test_file)
        
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])
        
        server = StringSearchServer(
            ("localhost", 0),
            default_config,
            server_config,
            ssl_config,
            logging_config,
            search_engine
        )
        
        assert server is not None
        assert server.server_address[0] == "localhost"
        
        # Clean up
        server.server_close()

    def test_server_socket_configuration(self, temp_dir: Path, test_file: Path):
        """Test server socket configuration."""
        default_config, server_config, ssl_config, logging_config = self.create_config_components(temp_dir, test_file)
        
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])
        
        server = StringSearchServer(
            ("localhost", 0),
            default_config,
            server_config,
            ssl_config,
            logging_config,
            search_engine
        )
        
        # Verify socket options are set
        assert server.socket.getsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR) == 1
        
        # Clean up
        server.server_close()


class TestSSLContext:
    """Test SSL context creation with new configuration system."""

    def test_ssl_disabled(self):
        """Test SSL context when SSL is disabled."""
        ssl_config: SSLConfig = {
            "ssl_enabled": False,
            "ssl_cert_file": "certs/server.crt",
            "ssl_key_file": "certs/server.key",
            "min_tls_version": "TLSv1.3",
            "verify_client_cert": False,
            "ca_file": "certs/ca.crt"
        }
        
        context = create_ssl_context(ssl_config)
        assert context is None

    def test_ssl_enabled_missing_files(self):
        """Test SSL context when SSL is enabled but files are missing."""
        ssl_config: SSLConfig = {
            "ssl_enabled": True,
            "ssl_cert_file": "/nonexistent/server.crt",
            "ssl_key_file": "/nonexistent/server.key",
            "min_tls_version": "TLSv1.3",
            "verify_client_cert": False,
            "ca_file": "/nonexistent/ca.crt"
        }
        
        with pytest.raises(FileSearchError):
            create_ssl_context(ssl_config)


class TestStringSearchHandler:
    """Test StringSearchHandler with new configuration system."""

    def test_handler_initialization(self):
        """Test handler can be initialized."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        assert handler is not None


class TestLoggingSetup:
    """Test logging setup functionality."""

    def test_setup_logging_levels(self):
        """Test logging setup with different levels."""
        levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
        
        for level in levels:
            # Should not raise any exceptions
            setup_logging(level)


class TestAllConfigurationScenarios:
    """Comprehensive tests for all 4 configuration scenarios."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def create_scenario_config(self, temp_dir: Path, test_file: Path, ssl_enabled: bool, reread_on_query: bool) -> Path:
        """Create configuration for a specific scenario."""
        config_file = temp_dir / "config.ini"
        config_content = f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = {str(reread_on_query).lower()}

[server]
host = localhost
port = 0
max_connections = 100
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[ssl]
ssl_enabled = {str(ssl_enabled).lower()}
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
min_tls_version = TLSv1.3
verify_client_cert = false
ca_file = certs/ca.crt

[logging]
log_level = DEBUG
log_file = logs/server.log
thread_pool_size = 50
"""
        config_file.write_text(config_content)
        return config_file

    @pytest.mark.parametrize("ssl_enabled,reread_on_query", [
        (True, True),   # SSL=true, REREAD=true
        (True, False),  # SSL=true, REREAD=false
        (False, True),  # SSL=false, REREAD=true
        (False, False), # SSL=false, REREAD=false
    ])
    def test_all_configuration_scenarios(self, temp_dir: Path, ssl_enabled: bool, reread_on_query: bool):
        """Test all four configuration scenarios."""
        # Create test file
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\ntest line 3\n")

        # Create configuration
        config_file = self.create_scenario_config(temp_dir, test_file, ssl_enabled, reread_on_query)

        # Load configuration
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))

        # Verify configuration
        assert default_config["reread_on_query"] == reread_on_query
        assert ssl_config["ssl_enabled"] == ssl_enabled

        # Test search engine
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])

        # Test search performance
        start_time = time.perf_counter()
        result = search_engine.search("test line 1")
        end_time = time.perf_counter()

        execution_time_ms = (end_time - start_time) * 1000
        max_time_ms = 40.0 if reread_on_query else 0.5

        assert result is True
        assert execution_time_ms <= max_time_ms, f"Performance requirement failed: {execution_time_ms:.2f}ms > {max_time_ms}ms"

        # Test server initialization
        server = StringSearchServer(
            ("localhost", 0),
            default_config,
            server_config,
            ssl_config,
            logging_config,
            search_engine
        )

        assert server is not None
        server.server_close()

    def test_server_with_actual_connections(self, temp_dir: Path):
        """Test server with actual socket connections (SSL disabled for simplicity)."""
        # Create test file
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\ntest line 3\n")

        # Create configuration (SSL disabled for simplicity)
        config_file = self.create_scenario_config(temp_dir, test_file, ssl_enabled=False, reread_on_query=False)

        # Load configuration and create server
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])

        server = StringSearchServer(
            ("localhost", 0),
            default_config,
            server_config,
            ssl_config,
            logging_config,
            search_engine
        )

        actual_port = server.server_address[1]

        # Start server in thread
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()

        try:
            # Wait for server to start
            time.sleep(0.1)

            # Test existing line
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall(b"test line 1\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING EXISTS\n"

            # Test non-existing line
            with socket.create_connection(("localhost", actual_port)) as sock:
                sock.sendall(b"nonexistent line\n")
                response = sock.recv(1024).decode('utf-8')
                assert response == "STRING NOT FOUND\n"

        finally:
            server.shutdown()
            server.server_close()
            server_thread.join(timeout=1)
