#!/usr/bin/env python3
"""
Load Testing Suite for TCP String Search Server

This module provides comprehensive load testing capabilities including:
- High concurrent connection testing (1 to 100+ clients)
- Queries per second (QPS) measurement
- Connection limit testing
- Server breaking point identification
- Resource utilization monitoring under load

Author: <PERSON>
Date: 2025
"""

import os
import sys
import time
import threading
import tempfile
import socket
import psutil
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import pytest

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from server import FileSearchEngine, StringSearchServer
from config_loader import load_config
from client import SearchClient


class LoadTestResult:
    """Container for load test results."""
    
    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_time = 0.0
        self.min_response_time = float('inf')
        self.max_response_time = 0.0
        self.response_times: List[float] = []
        self.errors: List[str] = []
        self.start_time = 0.0
        self.end_time = 0.0
    
    def add_result(self, success: bool, response_time: float, error: str = ""):
        """Add a single request result."""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
            self.response_times.append(response_time)
            self.min_response_time = min(self.min_response_time, response_time)
            self.max_response_time = max(self.max_response_time, response_time)
        else:
            self.failed_requests += 1
            if error:
                self.errors.append(error)
    
    def calculate_stats(self) -> Dict[str, float]:
        """Calculate performance statistics."""
        if not self.response_times:
            return {
                'avg_response_time': 0.0,
                'min_response_time': 0.0,
                'max_response_time': 0.0,
                'qps': 0.0,
                'success_rate': 0.0,
                'total_duration': self.end_time - self.start_time
            }
        
        total_duration = self.end_time - self.start_time
        avg_response_time = sum(self.response_times) / len(self.response_times)
        qps = self.successful_requests / total_duration if total_duration > 0 else 0
        success_rate = (self.successful_requests / self.total_requests) * 100 if self.total_requests > 0 else 0
        
        return {
            'avg_response_time': avg_response_time,
            'min_response_time': self.min_response_time,
            'max_response_time': self.max_response_time,
            'qps': qps,
            'success_rate': success_rate,
            'total_duration': total_duration
        }


def create_test_file_for_load(temp_dir: Path, size: int = 10000) -> Path:
    """Create a test file optimized for load testing."""
    test_file = temp_dir / "load_test.txt"
    
    with open(test_file, 'w') as f:
        for i in range(size):
            if i % 100 == 0:
                f.write(f"common_query_{i // 100}\n")
            else:
                f.write(f"line_{i:06d}_data\n")
    
    return test_file


def create_load_test_config(temp_dir: Path, file_path: str) -> Path:
    """Create configuration for load testing."""
    config_file = temp_dir / "load_test_config.ini"
    
    config_content = f"""[DEFAULT]
linuxpath = {file_path}
reread_on_query = false

[server]
host = localhost
port = 0
max_connections = 500
max_payload_size = 1024
connection_timeout = 10
tcp_nodelay = true
socket_buffer_size = 262144

[logging]
log_level = WARNING
"""
    
    config_file.write_text(config_content)
    return config_file


def worker_function(host: str, port: int, queries: List[str], 
                   requests_per_worker: int, worker_id: int) -> LoadTestResult:
    """Worker function for load testing."""
    result = LoadTestResult()
    result.start_time = time.time()
    
    try:
        client = SearchClient(host=host, port=port, timeout=5.0)
        
        for i in range(requests_per_worker):
            query = queries[i % len(queries)]
            try:
                response, exec_time = client.search(query)
                success = response in ["STRING EXISTS", "STRING NOT FOUND"]
                result.add_result(success, exec_time, "" if success else f"Invalid response: {response}")
            except Exception as e:
                result.add_result(False, 0.0, str(e))
                
    except Exception as e:
        result.errors.append(f"Worker {worker_id} failed to initialize: {e}")
    
    result.end_time = time.time()
    return result


class TestLoadTesting:
    """Load testing test cases."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)
    
    @pytest.fixture
    def test_server(self, temp_dir: Path):
        """Create and start a test server."""
        # Create test file and config
        test_file = create_test_file_for_load(temp_dir)
        config_file = create_load_test_config(temp_dir, str(test_file))
        
        # Initialize server components
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        search_engine = FileSearchEngine(str(test_file), False)
        server = StringSearchServer(("localhost", 0), default_config, server_config, ssl_config, logging_config, search_engine)
        
        # Start server in thread
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        # Wait for server to start
        time.sleep(0.1)
        
        yield server.server_address[0], server.server_address[1]
        
        # Cleanup
        server.shutdown()
        server.server_close()
        server_thread.join(timeout=1)
    
    def test_concurrent_connections_scaling(self, test_server: Tuple[str, int]):
        """Test server performance with increasing concurrent connections."""
        host, port = test_server
        
        # Test different connection counts
        connection_counts = [1, 5, 10, 25, 50, 100]
        requests_per_client = 10
        
        test_queries = [
            "common_query_0",
            "common_query_1", 
            "line_001000_data",
            "nonexistent_query",
            "common_query_5"
        ]
        
        results = []
        
        print("\nConcurrent Connection Scaling Test")
        print("=" * 60)
        print("Connections | QPS    | Avg RT | Success% | Errors")
        print("-" * 60)
        
        for conn_count in connection_counts:
            # Monitor system resources
            process = psutil.Process()
            cpu_before = process.cpu_percent()
            memory_before = process.memory_info().rss / 1024 / 1024
            
            # Run load test
            combined_result = LoadTestResult()
            combined_result.start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=conn_count) as executor:
                futures = []
                for i in range(conn_count):
                    future = executor.submit(
                        worker_function, host, port, test_queries, 
                        requests_per_client, i
                    )
                    futures.append(future)
                
                # Collect results
                for future in as_completed(futures):
                    worker_result = future.result()
                    combined_result.total_requests += worker_result.total_requests
                    combined_result.successful_requests += worker_result.successful_requests
                    combined_result.failed_requests += worker_result.failed_requests
                    combined_result.response_times.extend(worker_result.response_times)
                    combined_result.errors.extend(worker_result.errors)
            
            combined_result.end_time = time.time()
            
            # Calculate statistics
            stats = combined_result.calculate_stats()
            
            # Monitor system resources after
            cpu_after = process.cpu_percent()
            memory_after = process.memory_info().rss / 1024 / 1024
            
            result_data = {
                'connections': conn_count,
                'total_requests': combined_result.total_requests,
                'qps': stats['qps'],
                'avg_response_time': stats['avg_response_time'],
                'success_rate': stats['success_rate'],
                'error_count': len(combined_result.errors),
                'cpu_usage': cpu_after,
                'memory_usage_mb': memory_after
            }
            
            results.append(result_data)
            
            print(f"{conn_count:>10} | {stats['qps']:>6.1f} | {stats['avg_response_time']:>6.2f} | "
                  f"{stats['success_rate']:>7.1f}% | {len(combined_result.errors):>6}")
            
            # Stop if success rate drops significantly
            if stats['success_rate'] < 95.0:
                print(f"*** SUCCESS RATE DROPPED BELOW 95% AT {conn_count} CONNECTIONS ***")
                break
        
        print("-" * 60)
        
        # Verify basic performance requirements
        for result in results:
            if result['connections'] <= 25:  # Should handle at least 25 concurrent connections well
                assert result['success_rate'] >= 95.0, \
                    f"Success rate too low with {result['connections']} connections: {result['success_rate']:.1f}%"
                assert result['avg_response_time'] <= 50.0, \
                    f"Response time too high with {result['connections']} connections: {result['avg_response_time']:.2f}ms"
        
        return results

    def test_sustained_load(self, test_server: Tuple[str, int]):
        """Test server performance under sustained load."""
        host, port = test_server

        # Run sustained load for 30 seconds
        duration_seconds = 30
        concurrent_clients = 20

        test_queries = [
            "common_query_0",
            "line_005000_data",
            "nonexistent_query_123",
            "common_query_10"
        ]

        print(f"\nSustained Load Test ({duration_seconds}s with {concurrent_clients} clients)")
        print("=" * 60)

        # Monitor system resources
        process = psutil.Process()
        resource_samples = []

        def monitor_resources():
            """Monitor system resources during the test."""
            start_time = time.time()
            while time.time() - start_time < duration_seconds + 5:
                try:
                    cpu_percent = process.cpu_percent()
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    resource_samples.append({
                        'timestamp': time.time(),
                        'cpu_percent': cpu_percent,
                        'memory_mb': memory_mb
                    })
                    time.sleep(0.5)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    break

        # Start resource monitoring
        monitor_thread = threading.Thread(target=monitor_resources)
        monitor_thread.daemon = True
        monitor_thread.start()

        # Run sustained load
        def sustained_worker(worker_id: int) -> LoadTestResult:
            """Worker for sustained load testing."""
            result = LoadTestResult()
            result.start_time = time.time()

            client = SearchClient(host=host, port=port, timeout=5.0)

            while time.time() - result.start_time < duration_seconds:
                query = test_queries[worker_id % len(test_queries)]
                try:
                    response, exec_time = client.search(query)
                    success = response in ["STRING EXISTS", "STRING NOT FOUND"]
                    result.add_result(success, exec_time)
                except Exception as e:
                    result.add_result(False, 0.0, str(e))

                # Small delay to prevent overwhelming
                time.sleep(0.01)

            result.end_time = time.time()
            return result

        # Execute sustained load test
        combined_result = LoadTestResult()
        combined_result.start_time = time.time()

        with ThreadPoolExecutor(max_workers=concurrent_clients) as executor:
            futures = [executor.submit(sustained_worker, i) for i in range(concurrent_clients)]

            for future in as_completed(futures):
                worker_result = future.result()
                combined_result.total_requests += worker_result.total_requests
                combined_result.successful_requests += worker_result.successful_requests
                combined_result.failed_requests += worker_result.failed_requests
                combined_result.response_times.extend(worker_result.response_times)
                combined_result.errors.extend(worker_result.errors)

        combined_result.end_time = time.time()

        # Wait for monitoring to complete
        monitor_thread.join(timeout=2)

        # Calculate statistics
        stats = combined_result.calculate_stats()

        # Resource statistics
        if resource_samples:
            avg_cpu = sum(s['cpu_percent'] for s in resource_samples) / len(resource_samples)
            max_cpu = max(s['cpu_percent'] for s in resource_samples)
            avg_memory = sum(s['memory_mb'] for s in resource_samples) / len(resource_samples)
            max_memory = max(s['memory_mb'] for s in resource_samples)
        else:
            avg_cpu = max_cpu = avg_memory = max_memory = 0

        print(f"Duration: {stats['total_duration']:.1f}s")
        print(f"Total requests: {combined_result.total_requests:,}")
        print(f"Successful requests: {combined_result.successful_requests:,}")
        print(f"Failed requests: {combined_result.failed_requests:,}")
        print(f"Average QPS: {stats['qps']:.1f}")
        print(f"Average response time: {stats['avg_response_time']:.2f}ms")
        print(f"Success rate: {stats['success_rate']:.1f}%")
        print(f"CPU usage - Avg: {avg_cpu:.1f}%, Max: {max_cpu:.1f}%")
        print(f"Memory usage - Avg: {avg_memory:.1f}MB, Max: {max_memory:.1f}MB")

        # Verify sustained performance
        assert stats['success_rate'] >= 95.0, f"Sustained load success rate too low: {stats['success_rate']:.1f}%"
        assert stats['avg_response_time'] <= 100.0, f"Sustained load response time too high: {stats['avg_response_time']:.2f}ms"
        assert max_cpu <= 90.0, f"CPU usage too high during sustained load: {max_cpu:.1f}%"

        return {
            'stats': stats,
            'resource_usage': {
                'avg_cpu': avg_cpu,
                'max_cpu': max_cpu,
                'avg_memory': avg_memory,
                'max_memory': max_memory
            }
        }

    def test_connection_limit(self, test_server: Tuple[str, int]):
        """Test server behavior at connection limits."""
        host, port = test_server

        print("\nConnection Limit Test")
        print("=" * 40)

        # Try to establish many connections simultaneously
        max_connections_to_test = 300
        connection_batch_size = 50

        successful_connections = 0
        failed_connections = 0

        for batch_start in range(0, max_connections_to_test, connection_batch_size):
            batch_end = min(batch_start + connection_batch_size, max_connections_to_test)
            batch_size = batch_end - batch_start

            print(f"Testing connections {batch_start + 1}-{batch_end}...")

            connections = []
            batch_successful = 0
            batch_failed = 0

            # Try to establish connections in this batch
            for i in range(batch_size):
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2.0)
                    sock.connect((host, port))
                    connections.append(sock)
                    batch_successful += 1
                except Exception:
                    batch_failed += 1

            successful_connections += batch_successful
            failed_connections += batch_failed

            print(f"  Batch result: {batch_successful} successful, {batch_failed} failed")

            # Test if existing connections still work
            if connections:
                test_sock = connections[0]
                try:
                    test_sock.sendall(b"test_query\n")
                    response = test_sock.recv(1024)
                    if response:
                        print(f"  Existing connections still responsive")
                    else:
                        print(f"  WARNING: Existing connections not responsive")
                except Exception as e:
                    print(f"  WARNING: Connection test failed: {e}")

            # Close connections in this batch
            for sock in connections:
                try:
                    sock.close()
                except:
                    pass

            # If we start failing connections, we've found the limit
            if batch_failed > batch_successful:
                print(f"Connection limit reached around {successful_connections} connections")
                break

            # Small delay between batches
            time.sleep(0.1)

        print(f"\nConnection Limit Test Results:")
        print(f"Total successful connections: {successful_connections}")
        print(f"Total failed connections: {failed_connections}")

        # Server should handle at least 100 concurrent connections
        assert successful_connections >= 100, \
            f"Server should handle at least 100 connections, got {successful_connections}"

        return {
            'max_successful_connections': successful_connections,
            'failed_connections': failed_connections
        }
