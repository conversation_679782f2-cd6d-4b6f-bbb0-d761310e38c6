#!/usr/bin/env python3
"""
Comprehensive configuration testing for the new TypedDict-based configuration system.

This module tests all configuration scenarios including SSL and REREAD_ON_QUERY combinations.
"""

import os
import tempfile
import pytest
from pathlib import Path
from typing import Dict, Any

from config_loader import (
    load_config, get_file_path, find_config_file, ConfigurationError,
    DefaultConfig, ServerConfig, SSLConfig, LoggingConfig
)
from server import FileSearchEngine, StringSearchServer, create_ssl_context


class TestConfigurationScenarios:
    """Test all configuration scenarios as required."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file for configuration testing."""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line 1\ntest line 2\ntest line 3\n")
        return test_file

    def create_config_file(self, temp_dir: Path, test_file: Path, ssl_enabled: bool, reread_on_query: bool) -> Path:
        """Create a configuration file with specified settings."""
        config_file = temp_dir / "config.ini"
        
        config_content = f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = {str(reread_on_query).lower()}

[server]
host = localhost
port = 0
max_connections = 100
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[ssl]
ssl_enabled = {str(ssl_enabled).lower()}
ssl_cert_file = certs/server.crt
ssl_key_file = certs/server.key
min_tls_version = TLSv1.3
verify_client_cert = false
ca_file = certs/ca.crt

[logging]
log_level = DEBUG
log_file = logs/server.log
thread_pool_size = 50
"""
        config_file.write_text(config_content)
        return config_file

    def test_configuration_scenario_ssl_true_reread_true(self, temp_dir: Path, test_file: Path):
        """Test SSL=true, REREAD_ON_QUERY=true scenario."""
        config_file = self.create_config_file(temp_dir, test_file, ssl_enabled=True, reread_on_query=True)
        
        # Test configuration loading
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        # Verify configuration values
        assert default_config["reread_on_query"] is True
        assert ssl_config["ssl_enabled"] is True
        assert server_config["host"] == "localhost"
        assert logging_config["log_level"] == "DEBUG"
        
        # Test file search engine with this configuration
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])
        assert search_engine.reread_on_query is True
        
        # Test search functionality
        result = search_engine.search("test line 1")
        assert result is True
        
        result = search_engine.search("nonexistent line")
        assert result is False

    def test_configuration_scenario_ssl_true_reread_false(self, temp_dir: Path, test_file: Path):
        """Test SSL=true, REREAD_ON_QUERY=false scenario."""
        config_file = self.create_config_file(temp_dir, test_file, ssl_enabled=True, reread_on_query=False)
        
        # Test configuration loading
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        # Verify configuration values
        assert default_config["reread_on_query"] is False
        assert ssl_config["ssl_enabled"] is True
        
        # Test file search engine with this configuration
        search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])
        assert search_engine.reread_on_query is False
        
        # Test search functionality
        result = search_engine.search("test line 2")
        assert result is True

    def test_configuration_scenario_ssl_false_reread_true(self, temp_dir: Path, test_file: Path):
        """Test SSL=false, REREAD_ON_QUERY=true scenario."""
        config_file = self.create_config_file(temp_dir, test_file, ssl_enabled=False, reread_on_query=True)
        
        # Test configuration loading
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        # Verify configuration values
        assert default_config["reread_on_query"] is True
        assert ssl_config["ssl_enabled"] is False
        
        # Test SSL context creation (should return None)
        ssl_context = create_ssl_context(ssl_config)
        assert ssl_context is None

    def test_configuration_scenario_ssl_false_reread_false(self, temp_dir: Path, test_file: Path):
        """Test SSL=false, REREAD_ON_QUERY=false scenario."""
        config_file = self.create_config_file(temp_dir, test_file, ssl_enabled=False, reread_on_query=False)
        
        # Test configuration loading
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        # Verify configuration values
        assert default_config["reread_on_query"] is False
        assert ssl_config["ssl_enabled"] is False
        
        # Test SSL context creation (should return None)
        ssl_context = create_ssl_context(ssl_config)
        assert ssl_context is None

    def test_server_initialization_all_scenarios(self, temp_dir: Path, test_file: Path):
        """Test server initialization with all configuration scenarios."""
        scenarios = [
            (True, True),   # SSL=true, REREAD=true
            (True, False),  # SSL=true, REREAD=false
            (False, True),  # SSL=false, REREAD=true
            (False, False), # SSL=false, REREAD=false
        ]
        
        for ssl_enabled, reread_on_query in scenarios:
            config_file = self.create_config_file(temp_dir, test_file, ssl_enabled, reread_on_query)
            
            # Load configuration
            default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
            
            # Create search engine
            search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])
            
            # Create server (should not raise exceptions)
            server = StringSearchServer(
                ("localhost", 0), 
                default_config, 
                server_config, 
                ssl_config, 
                logging_config, 
                search_engine
            )
            
            # Verify server was created successfully
            assert server is not None
            assert server.server_address[0] == "localhost"
            
            # Clean up
            server.server_close()

    def test_configuration_error_handling(self, temp_dir: Path):
        """Test configuration error handling."""
        # Test missing file
        with pytest.raises(ConfigurationError):
            load_config("nonexistent_config.ini")
        
        # Test malformed config
        malformed_config = temp_dir / "malformed.ini"
        malformed_config.write_text("invalid ini content [[[")
        
        with pytest.raises(ConfigurationError):
            load_config(str(malformed_config))
        
        # Test missing required parameter
        incomplete_config = temp_dir / "incomplete.ini"
        incomplete_config.write_text("""[DEFAULT]
# Missing linuxpath
reread_on_query = false
""")
        
        with pytest.raises(ConfigurationError):
            load_config(str(incomplete_config))

    def test_configuration_type_validation(self, temp_dir: Path, test_file: Path):
        """Test configuration type validation."""
        config_file = self.create_config_file(temp_dir, test_file, ssl_enabled=True, reread_on_query=False)
        
        # Load configuration
        default_config, server_config, ssl_config, logging_config = load_config(str(config_file))
        
        # Verify types
        assert isinstance(default_config["reread_on_query"], bool)
        assert isinstance(server_config["port"], int)
        assert isinstance(ssl_config["ssl_enabled"], bool)
        assert isinstance(logging_config["log_level"], str)
        
        # Verify TypedDict structure
        assert "linuxpath" in default_config
        assert "host" in server_config
        assert "ssl_cert_file" in ssl_config
        assert "thread_pool_size" in logging_config

    def test_file_path_resolution(self, temp_dir: Path, test_file: Path):
        """Test file path resolution functionality."""
        config_file = self.create_config_file(temp_dir, test_file, ssl_enabled=False, reread_on_query=False)
        
        # Test get_file_path function
        resolved_path = get_file_path(str(config_file))
        assert os.path.exists(resolved_path)
        assert resolved_path == str(test_file)

    def test_performance_requirements_all_scenarios(self, temp_dir: Path, test_file: Path):
        """Test that performance requirements are met in all configuration scenarios."""
        import time
        
        scenarios = [
            (True, True, 40.0),   # SSL=true, REREAD=true, max 40ms
            (True, False, 0.5),   # SSL=true, REREAD=false, max 0.5ms
            (False, True, 40.0),  # SSL=false, REREAD=true, max 40ms
            (False, False, 0.5),  # SSL=false, REREAD=false, max 0.5ms
        ]
        
        for ssl_enabled, reread_on_query, max_time_ms in scenarios:
            config_file = self.create_config_file(temp_dir, test_file, ssl_enabled, reread_on_query)
            
            # Load configuration and create search engine
            default_config, _, _, _ = load_config(str(config_file))
            search_engine = FileSearchEngine(str(test_file), default_config["reread_on_query"])
            
            # Test search performance
            start_time = time.perf_counter()
            result = search_engine.search("test line 1")
            end_time = time.perf_counter()
            
            execution_time_ms = (end_time - start_time) * 1000
            
            # Verify performance requirement
            assert execution_time_ms <= max_time_ms, \
                f"Performance requirement failed for SSL={ssl_enabled}, REREAD={reread_on_query}: " \
                f"{execution_time_ms:.2f}ms > {max_time_ms}ms"
            
            # Verify search worked
            assert result is True


class TestConfigurationIntegration:
    """Integration tests for configuration with server functionality."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_configuration_with_actual_config_file(self):
        """Test configuration loading with the actual config.ini file."""
        # This tests the real configuration file
        try:
            default_config, server_config, ssl_config, logging_config = load_config()
            
            # Verify all sections loaded
            assert "linuxpath" in default_config
            assert "host" in server_config
            assert "ssl_enabled" in ssl_config
            assert "log_level" in logging_config
            
            # Verify types are correct
            assert isinstance(default_config["reread_on_query"], bool)
            assert isinstance(server_config["port"], int)
            assert isinstance(ssl_config["ssl_enabled"], bool)
            
        except ConfigurationError:
            # If the main config file doesn't exist, that's also a valid test result
            pytest.skip("Main config.ini file not found")

    def test_find_config_file_functionality(self):
        """Test the find_config_file functionality."""
        try:
            config_file = find_config_file()
            assert os.path.exists(config_file)
            assert config_file.endswith("config.ini")
        except ConfigurationError:
            # If no config file is found, that's expected in some environments
            pytest.skip("No config file found in standard locations")
