#!/usr/bin/env python3
"""
Comprehensive Test Suite for Server Module

This module provides exhaustive testing for all server components,
ensuring 100% code coverage and robust error handling.

Test Coverage:
- FileSearchEngine with all configurations
- StringSearchServer with all socket options
- StringSearchHandler with all request scenarios
- SSL context creation and configuration
- Logging setup and configuration
- Error handling and exception scenarios
- Performance validation
- Network protocol compliance
- Unicode and encoding handling
- Concurrent connection handling

Author: <PERSON>
Date: 2025
"""

import os
import sys
import tempfile
import threading
import time
import socket
import ssl
import logging
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from server import (
    FileSearchEngine, StringSearchServer, StringSearchHandler,
    create_ssl_context, FileSearchError, setup_logging, main
)
from config_loader import (
    load_config, DefaultConfig, ServerConfig, SSLConfig, LoggingConfig
)


class TestFileSearchEngineComprehensive:
    """Comprehensive tests for FileSearchEngine class."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file with known content."""
        test_file = temp_dir / "test.txt"
        content = "line1\nline2\nline3\nspecial chars: àáâã\nempty line below\n\nlast line\n"
        test_file.write_text(content, encoding='utf-8')
        return test_file

    def test_file_search_engine_initialization_cached_mode(self, test_file: Path):
        """Test FileSearchEngine initialization in cached mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=False)
        
        assert engine.file_path == str(test_file)
        assert engine.reread_on_query is False
        assert engine.searcher is not None
        assert hasattr(engine.searcher, 'search')

    def test_file_search_engine_initialization_reread_mode(self, test_file: Path):
        """Test FileSearchEngine initialization in reread mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=True)
        
        assert engine.file_path == str(test_file)
        assert engine.reread_on_query is True
        assert engine.searcher is not None

    def test_file_search_engine_file_validation_success(self, test_file: Path):
        """Test successful file validation."""
        # Should not raise any exception
        engine = FileSearchEngine(str(test_file), reread_on_query=False)
        assert engine.file_path == str(test_file)

    def test_file_search_engine_file_validation_not_found(self):
        """Test file validation with non-existent file."""
        with pytest.raises(FileSearchError, match="File not found"):
            FileSearchEngine("/nonexistent/file.txt", reread_on_query=False)

    def test_file_search_engine_file_validation_not_readable(self, temp_dir: Path):
        """Test file validation with unreadable file."""
        test_file = temp_dir / "unreadable.txt"
        test_file.write_text("test content")
        test_file.chmod(0o000)  # Remove all permissions
        
        try:
            with pytest.raises(FileSearchError, match="File not readable"):
                FileSearchEngine(str(test_file), reread_on_query=False)
        finally:
            test_file.chmod(0o644)  # Restore permissions

    def test_file_search_engine_search_cached_mode(self, test_file: Path):
        """Test search functionality in cached mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=False)
        
        assert engine.search("line1") is True
        assert engine.search("line2") is True
        assert engine.search("line3") is True
        assert engine.search("special chars: àáâã") is True
        assert engine.search("") is True  # Empty line
        assert engine.search("last line") is True
        assert engine.search("nonexistent") is False

    def test_file_search_engine_search_reread_mode(self, test_file: Path):
        """Test search functionality in reread mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=True)
        
        assert engine.search("line1") is True
        assert engine.search("line2") is True
        assert engine.search("line3") is True
        assert engine.search("nonexistent") is False

    def test_file_search_engine_search_performance_cached(self, test_file: Path):
        """Test search performance in cached mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=False)
        
        start_time = time.perf_counter()
        result = engine.search("line1")
        end_time = time.perf_counter()
        
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        assert execution_time_ms <= 0.5  # Performance requirement

    def test_file_search_engine_search_performance_reread(self, test_file: Path):
        """Test search performance in reread mode."""
        engine = FileSearchEngine(str(test_file), reread_on_query=True)
        
        start_time = time.perf_counter()
        result = engine.search("line1")
        end_time = time.perf_counter()
        
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        assert execution_time_ms <= 40.0  # Performance requirement

    def test_file_search_engine_search_exception_handling(self, test_file: Path):
        """Test search exception handling."""
        engine = FileSearchEngine(str(test_file), reread_on_query=False)
        
        # Mock the searcher to raise an exception
        with patch.object(engine.searcher, 'search', side_effect=Exception("Search failed")):
            with pytest.raises(FileSearchError, match="Search failed"):
                engine.search("test query")

    def test_file_search_engine_unicode_handling(self, temp_dir: Path):
        """Test FileSearchEngine with Unicode content."""
        unicode_file = temp_dir / "unicode.txt"
        unicode_content = "English line\n中文行\nрусская строка\n🚀 emoji line\nمرحبا\n"
        unicode_file.write_text(unicode_content, encoding='utf-8')
        
        engine = FileSearchEngine(str(unicode_file), reread_on_query=False)
        
        assert engine.search("English line") is True
        assert engine.search("中文行") is True
        assert engine.search("русская строка") is True
        assert engine.search("🚀 emoji line") is True
        assert engine.search("مرحبا") is True

    def test_file_search_engine_large_file(self, temp_dir: Path):
        """Test FileSearchEngine with a larger file."""
        large_file = temp_dir / "large.txt"
        
        # Create a file with 1000 lines
        lines = [f"line_{i:04d}" for i in range(1000)]
        large_file.write_text("\n".join(lines) + "\n")
        
        engine = FileSearchEngine(str(large_file), reread_on_query=False)
        
        assert engine.search("line_0000") is True
        assert engine.search("line_0500") is True
        assert engine.search("line_0999") is True
        assert engine.search("line_1000") is False

    def test_file_search_engine_empty_file(self, temp_dir: Path):
        """Test FileSearchEngine with an empty file."""
        empty_file = temp_dir / "empty.txt"
        empty_file.write_text("")
        
        engine = FileSearchEngine(str(empty_file), reread_on_query=False)
        
        assert engine.search("anything") is False
        assert engine.search("") is False


class TestSetupLoggingFunction:
    """Comprehensive tests for setup_logging function."""

    def test_setup_logging_default_parameters(self):
        """Test setup_logging with default parameters."""
        # Should not raise any exception
        setup_logging()

    def test_setup_logging_custom_log_level(self):
        """Test setup_logging with custom log level."""
        log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        
        for level in log_levels:
            setup_logging(log_level=level)
            # Verify that the logging level is set correctly
            assert logging.getLogger().level == getattr(logging, level)

    def test_setup_logging_custom_log_file(self, tmp_path):
        """Test setup_logging with custom log file."""
        log_file = tmp_path / "custom.log"
        
        setup_logging(log_level="INFO", log_file=str(log_file))
        
        # Log a test message
        logging.info("Test message")
        
        # Verify that the log file was created
        assert log_file.exists()

    def test_setup_logging_creates_log_directory(self, tmp_path):
        """Test that setup_logging creates log directory if it doesn't exist."""
        log_dir = tmp_path / "logs"
        log_file = log_dir / "test.log"
        
        # Directory doesn't exist initially
        assert not log_dir.exists()
        
        setup_logging(log_level="INFO", log_file=str(log_file))
        
        # Directory should be created
        assert log_dir.exists()
        assert log_dir.is_dir()

    def test_setup_logging_with_empty_log_file(self):
        """Test setup_logging with empty log file parameter."""
        # Should use default log file
        setup_logging(log_level="INFO", log_file="")

    def test_setup_logging_with_none_log_file(self):
        """Test setup_logging with None log file parameter."""
        # Should use default log file
        setup_logging(log_level="INFO", log_file=None)

    def test_setup_logging_invalid_log_level(self):
        """Test setup_logging with invalid log level."""
        # Should handle invalid log level gracefully
        with pytest.raises(AttributeError):
            setup_logging(log_level="INVALID_LEVEL")

    def test_setup_logging_case_insensitive_log_level(self):
        """Test setup_logging with case variations in log level."""
        # Should handle case variations
        setup_logging(log_level="debug")
        assert logging.getLogger().level == logging.DEBUG
        
        setup_logging(log_level="Info")
        assert logging.getLogger().level == logging.INFO


class TestStringSearchHandlerComprehensive:
    """Comprehensive tests for StringSearchHandler class."""

    def test_string_search_handler_initialization(self):
        """Test StringSearchHandler initialization."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        assert handler.request == mock_request
        assert handler.client_address == mock_client_address
        assert handler.server == mock_server

    def test_string_search_handler_handle_method_basic(self):
        """Test StringSearchHandler handle method with basic request."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        # Mock server configuration
        mock_server.server_config = {"max_payload_size": 1024}
        
        # Mock search engine
        mock_search_engine = Mock()
        mock_search_engine.search.return_value = True
        mock_server.search_engine = mock_search_engine
        
        # Mock request data
        mock_request.recv.return_value = b"test query\n"
        mock_request.sendall = Mock()
        mock_request.shutdown = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        # Call handle method
        handler.handle()
        
        # Verify interactions
        mock_request.recv.assert_called_once_with(1024)
        mock_search_engine.search.assert_called_once_with("test query")
        mock_request.sendall.assert_called_once_with(b"STRING EXISTS\n")

    def test_string_search_handler_handle_method_not_found(self):
        """Test StringSearchHandler handle method with string not found."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        # Mock server configuration
        mock_server.server_config = {"max_payload_size": 1024}
        
        # Mock search engine
        mock_search_engine = Mock()
        mock_search_engine.search.return_value = False
        mock_server.search_engine = mock_search_engine
        
        # Mock request data
        mock_request.recv.return_value = b"nonexistent query\n"
        mock_request.sendall = Mock()
        mock_request.shutdown = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        # Call handle method
        handler.handle()
        
        # Verify interactions
        mock_search_engine.search.assert_called_once_with("nonexistent query")
        mock_request.sendall.assert_called_once_with(b"STRING NOT FOUND\n")

    def test_string_search_handler_handle_empty_request(self):
        """Test StringSearchHandler handle method with empty request."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        # Mock server configuration
        mock_server.server_config = {"max_payload_size": 1024}
        
        # Mock request data (empty)
        mock_request.recv.return_value = b""
        mock_request.sendall = Mock()
        mock_request.shutdown = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        # Call handle method
        handler.handle()
        
        # Verify that no response is sent for empty request
        mock_request.sendall.assert_not_called()

    def test_string_search_handler_handle_null_terminated_query(self):
        """Test StringSearchHandler handle method with null-terminated query."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        # Mock server configuration
        mock_server.server_config = {"max_payload_size": 1024}
        
        # Mock search engine
        mock_search_engine = Mock()
        mock_search_engine.search.return_value = True
        mock_server.search_engine = mock_search_engine
        
        # Mock request data with null characters
        mock_request.recv.return_value = b"test query\x00\x00\x00"
        mock_request.sendall = Mock()
        mock_request.shutdown = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        # Call handle method
        handler.handle()
        
        # Verify that null characters are stripped
        mock_search_engine.search.assert_called_once_with("test query")
        mock_request.sendall.assert_called_once_with(b"STRING EXISTS\n")

    def test_string_search_handler_handle_unicode_query(self):
        """Test StringSearchHandler handle method with Unicode query."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        # Mock server configuration
        mock_server.server_config = {"max_payload_size": 1024}
        
        # Mock search engine
        mock_search_engine = Mock()
        mock_search_engine.search.return_value = True
        mock_server.search_engine = mock_search_engine
        
        # Mock request data with Unicode
        unicode_query = "测试查询"
        mock_request.recv.return_value = unicode_query.encode('utf-8')
        mock_request.sendall = Mock()
        mock_request.shutdown = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        # Call handle method
        handler.handle()
        
        # Verify Unicode handling
        mock_search_engine.search.assert_called_once_with(unicode_query)
        mock_request.sendall.assert_called_once_with(b"STRING EXISTS\n")

    def test_string_search_handler_handle_unicode_decode_error(self):
        """Test StringSearchHandler handle method with Unicode decode error."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        # Mock server configuration
        mock_server.server_config = {"max_payload_size": 1024}
        
        # Mock request data with invalid UTF-8
        mock_request.recv.return_value = b"\xff\xfe\x00\x00invalid utf-8"
        mock_request.sendall = Mock()
        mock_request.shutdown = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        # Call handle method
        handler.handle()
        
        # Should send NOT FOUND response for decode errors
        mock_request.sendall.assert_called_once_with(b"STRING NOT FOUND\n")

    def test_string_search_handler_handle_search_exception(self):
        """Test StringSearchHandler handle method with search exception."""
        mock_request = Mock()
        mock_client_address = ("127.0.0.1", 12345)
        mock_server = Mock()
        
        # Mock server configuration
        mock_server.server_config = {"max_payload_size": 1024}
        
        # Mock search engine that raises exception
        mock_search_engine = Mock()
        mock_search_engine.search.side_effect = Exception("Search failed")
        mock_server.search_engine = mock_search_engine
        
        # Mock request data
        mock_request.recv.return_value = b"test query\n"
        mock_request.sendall = Mock()
        mock_request.shutdown = Mock()
        
        handler = StringSearchHandler(mock_request, mock_client_address, mock_server)
        
        # Call handle method
        handler.handle()
        
        # Should send NOT FOUND response for exceptions
        mock_request.sendall.assert_called_once_with(b"STRING NOT FOUND\n")
