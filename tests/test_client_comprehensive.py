#!/usr/bin/env python3
"""
Comprehensive Test Suite for TCP String Search Client

This module provides exhaustive testing for the TCPStringSearchClient class,
ensuring 100% code coverage and robust error handling for all client operations.

Test Coverage:
- Client initialization with various parameters
- Connection establishment and management
- Query sending and response handling
- SSL/TLS functionality
- Persistent connection management
- Error handling and exception scenarios
- Timeout handling
- Unicode and encoding scenarios
- Performance validation

Author: <PERSON>
Date: 2025
"""

import os
import sys
import tempfile
import threading
import time
import socket
import ssl
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from client import SearchClient
from server import StringSearchServer, FileSearchEngine
from config_loader import load_config


class TestTCPStringSearchClientInitialization:
    """Test client initialization with various parameters."""

    def test_client_initialization_basic(self):
        """Test basic client initialization."""
        client = TCPStringSearchClient("localhost", 8888)
        
        assert client.host == "localhost"
        assert client.port == 8888
        assert client.timeout == 5.0  # Default timeout
        assert client.use_ssl is False  # Default SSL setting
        assert client._connection is None  # No connection initially

    def test_client_initialization_with_timeout(self):
        """Test client initialization with custom timeout."""
        client = TCPStringSearchClient("localhost", 8888, timeout=10.0)
        
        assert client.timeout == 10.0

    def test_client_initialization_with_ssl(self):
        """Test client initialization with SSL enabled."""
        client = TCPStringSearchClient("localhost", 8888, use_ssl=True)
        
        assert client.use_ssl is True

    def test_client_initialization_with_all_parameters(self):
        """Test client initialization with all parameters."""
        client = TCPStringSearchClient("example.com", 9999, timeout=15.0, use_ssl=True)
        
        assert client.host == "example.com"
        assert client.port == 9999
        assert client.timeout == 15.0
        assert client.use_ssl is True

    def test_client_initialization_with_ipv6_address(self):
        """Test client initialization with IPv6 address."""
        client = TCPStringSearchClient("::1", 8888)
        
        assert client.host == "::1"
        assert client.port == 8888

    def test_client_initialization_with_zero_timeout(self):
        """Test client initialization with zero timeout."""
        client = TCPStringSearchClient("localhost", 8888, timeout=0.0)
        
        assert client.timeout == 0.0

    def test_client_initialization_with_negative_timeout(self):
        """Test client initialization with negative timeout."""
        client = TCPStringSearchClient("localhost", 8888, timeout=-1.0)
        
        assert client.timeout == -1.0

    def test_client_initialization_with_very_large_timeout(self):
        """Test client initialization with very large timeout."""
        client = TCPStringSearchClient("localhost", 8888, timeout=3600.0)
        
        assert client.timeout == 3600.0

    def test_client_initialization_with_port_zero(self):
        """Test client initialization with port 0."""
        client = TCPStringSearchClient("localhost", 0)
        
        assert client.port == 0

    def test_client_initialization_with_high_port(self):
        """Test client initialization with high port number."""
        client = TCPStringSearchClient("localhost", 65535)
        
        assert client.port == 65535


class TestTCPStringSearchClientSSLContext:
    """Test SSL context creation and configuration."""

    def test_create_ssl_context_basic(self):
        """Test basic SSL context creation."""
        client = TCPStringSearchClient("localhost", 8888, use_ssl=True)
        
        context = client.create_ssl_context()
        
        assert isinstance(context, ssl.SSLContext)
        assert context.check_hostname is False
        assert context.verify_mode == ssl.CERT_NONE

    def test_create_ssl_context_with_verification_disabled(self):
        """Test SSL context creation with verification disabled."""
        client = TCPStringSearchClient("localhost", 8888, use_ssl=True)
        
        context = client.create_ssl_context()
        
        # Verify that certificate verification is disabled for testing
        assert context.verify_mode == ssl.CERT_NONE
        assert context.check_hostname is False

    def test_create_ssl_context_protocol_version(self):
        """Test SSL context uses appropriate protocol version."""
        client = TCPStringSearchClient("localhost", 8888, use_ssl=True)
        
        context = client.create_ssl_context()
        
        # Verify that a modern SSL context is created
        assert hasattr(context, 'minimum_version')

    def test_create_ssl_context_multiple_calls(self):
        """Test that multiple calls to create_ssl_context work correctly."""
        client = TCPStringSearchClient("localhost", 8888, use_ssl=True)
        
        context1 = client.create_ssl_context()
        context2 = client.create_ssl_context()
        
        # Both should be valid SSL contexts
        assert isinstance(context1, ssl.SSLContext)
        assert isinstance(context2, ssl.SSLContext)


class TestTCPStringSearchClientConnectionManagement:
    """Test connection establishment and management."""

    def test_connect_method_without_server(self):
        """Test connect method when no server is available."""
        client = TCPStringSearchClient("*********", 12345, timeout=0.1)  # TEST-NET-1
        
        with pytest.raises(TimeoutError, match="Connection to *********:12345 timed out"):
            client.connect()

    def test_connect_method_with_invalid_host(self):
        """Test connect method with invalid hostname."""
        client = TCPStringSearchClient("invalid.nonexistent.hostname", 8888, timeout=1.0)
        
        with pytest.raises(ConnectionError):
            client.connect()

    def test_disconnect_method_without_connection(self):
        """Test disconnect method when no connection exists."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Should not raise any exception
        client.disconnect()
        assert client._connection is None

    def test_disconnect_method_with_mock_connection(self):
        """Test disconnect method with a mock connection."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Create a mock connection
        mock_connection = Mock()
        client._connection = mock_connection
        
        client.disconnect()
        
        # Verify that close was called and connection is None
        mock_connection.close.assert_called_once()
        assert client._connection is None

    def test_disconnect_method_with_exception_in_close(self):
        """Test disconnect method when close raises an exception."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Create a mock connection that raises an exception on close
        mock_connection = Mock()
        mock_connection.close.side_effect = Exception("Close failed")
        client._connection = mock_connection
        
        # Should not raise an exception
        client.disconnect()
        assert client._connection is None

    def test_persistent_connection_reuse(self):
        """Test that persistent connections are reused."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Mock the connection
        mock_connection = Mock()
        client._connection = mock_connection
        
        # Call send_query_persistent multiple times
        mock_connection.sendall.return_value = None
        mock_connection.recv.return_value = b"STRING EXISTS\n"
        
        result1 = client.send_query_persistent("test1")
        result2 = client.send_query_persistent("test2")
        
        # Verify the same connection was used
        assert result1 == "STRING EXISTS"
        assert result2 == "STRING EXISTS"
        assert mock_connection.sendall.call_count == 2


class TestTCPStringSearchClientQueryHandling:
    """Test query sending and response handling."""

    def test_send_query_with_mock_server(self):
        """Test send_query with a mock server response."""
        client = TCPStringSearchClient("localhost", 8888, timeout=1.0)
        
        # Mock socket operations
        with patch('socket.socket') as mock_socket_class:
            mock_socket = Mock()
            mock_socket_class.return_value = mock_socket
            mock_socket.recv.return_value = b"STRING EXISTS\n"
            
            response, execution_time = client.send_query("test query")
            
            assert response == "STRING EXISTS"
            assert isinstance(execution_time, float)
            assert execution_time >= 0

    def test_send_query_with_string_not_found_response(self):
        """Test send_query with STRING NOT FOUND response."""
        client = TCPStringSearchClient("localhost", 8888, timeout=1.0)
        
        with patch('socket.socket') as mock_socket_class:
            mock_socket = Mock()
            mock_socket_class.return_value = mock_socket
            mock_socket.recv.return_value = b"STRING NOT FOUND\n"
            
            response, execution_time = client.send_query("nonexistent query")
            
            assert response == "STRING NOT FOUND"
            assert isinstance(execution_time, float)

    def test_send_query_with_empty_response(self):
        """Test send_query with empty response."""
        client = TCPStringSearchClient("localhost", 8888, timeout=1.0)
        
        with patch('socket.socket') as mock_socket_class:
            mock_socket = Mock()
            mock_socket_class.return_value = mock_socket
            mock_socket.recv.return_value = b""
            
            response, execution_time = client.send_query("test query")
            
            assert response == ""
            assert isinstance(execution_time, float)

    def test_send_query_with_whitespace_response(self):
        """Test send_query with whitespace-only response."""
        client = TCPStringSearchClient("localhost", 8888, timeout=1.0)
        
        with patch('socket.socket') as mock_socket_class:
            mock_socket = Mock()
            mock_socket_class.return_value = mock_socket
            mock_socket.recv.return_value = b"   \n\t  \n"
            
            response, execution_time = client.send_query("test query")
            
            assert response == ""  # Should be stripped
            assert isinstance(execution_time, float)

    def test_send_query_with_long_response(self):
        """Test send_query with a long response."""
        client = TCPStringSearchClient("localhost", 8888, timeout=1.0)
        
        long_response = "A" * 500 + "\n"
        
        with patch('socket.socket') as mock_socket_class:
            mock_socket = Mock()
            mock_socket_class.return_value = mock_socket
            mock_socket.recv.return_value = long_response.encode('utf-8')
            
            response, execution_time = client.send_query("test query")
            
            assert response == "A" * 500
            assert isinstance(execution_time, float)

    def test_send_query_persistent_with_mock_connection(self):
        """Test send_query_persistent with a mock connection."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Mock the connection
        mock_connection = Mock()
        mock_connection.sendall.return_value = None
        mock_connection.recv.return_value = b"STRING EXISTS\n"
        client._connection = mock_connection
        
        response = client.send_query_persistent("test query")
        
        assert response == "STRING EXISTS"
        mock_connection.sendall.assert_called_once()
        mock_connection.recv.assert_called_once()

    def test_send_query_persistent_without_connection(self):
        """Test send_query_persistent when no connection exists."""
        client = TCPStringSearchClient("*********", 12345, timeout=0.1)
        
        with pytest.raises(TimeoutError):
            client.send_query_persistent("test query")

    def test_query_length_validation(self):
        """Test query length validation."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Test query that's exactly 1024 bytes
        query_1024 = "x" * 1024
        
        with patch('socket.socket') as mock_socket_class:
            mock_socket = Mock()
            mock_socket_class.return_value = mock_socket
            mock_socket.recv.return_value = b"STRING EXISTS\n"
            
            response, execution_time = client.send_query(query_1024)
            assert response == "STRING EXISTS"

    def test_query_too_long_validation(self):
        """Test query length validation for queries that are too long."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Test query that's longer than 1024 bytes
        query_too_long = "x" * 1025
        
        with pytest.raises(ValueError, match="Query too long"):
            client.send_query(query_too_long)

    def test_query_too_long_persistent_validation(self):
        """Test query length validation for persistent queries that are too long."""
        client = TCPStringSearchClient("localhost", 8888)
        
        # Test query that's longer than 1024 bytes
        query_too_long = "x" * 1025
        
        with pytest.raises(ValueError, match="Query too long"):
            client.send_query_persistent(query_too_long)
